# Đặc tả Use Case - <PERSON><PERSON> công Giáo viên

## Thông tin chung

| Mục | <PERSON><PERSON> tả |
|-----|-------|
| **Tên use case** | Phân công giáo viên cho môn học |
| **Tóm tắt** | Use case mô tả quá trình quản trị viên thực hiện phân công giáo viên giảng dạy các môn học trong hệ thống |
| **Tác nhân** | Admin (Quản trị viên) |
| **Use case liên quan** | - L<PERSON><PERSON> danh sách giáo viên (include)<br/>- <PERSON><PERSON><PERSON> danh sách môn học (include)<br/>- Ki<PERSON>m tra phân công tồn tại (include)<br/>- <PERSON><PERSON><PERSON> thực quyền quản trị (include) |

## Dòng sự kiện chính

1. <PERSON><PERSON> đăng nhập vào hệ thống
2. <PERSON><PERSON> thống xác thực quyền quản trị của Admin
3. <PERSON><PERSON> chọn chức năng "Phân công môn học cho giáo viên" từ menu
4. Hệ thống hiển thị giao diện phân công giáo viên với:
   - Danh sách tất cả giáo viên (vai trò Teacher)
   - Danh sách tất cả môn học
   - Danh sách phân công hiện tại
5. Admin thực hiện thao tác:
   - **Thêm phân công**: Chuyển đến dòng sự kiện A
   - **Xóa phân công**: Chuyển đến dòng sự kiện B
   - **Xem phân công theo giáo viên**: Chuyển đến dòng sự kiện C

### Dòng sự kiện A - Thêm phân công giáo viên

A1. Admin chọn giáo viên từ danh sách giáo viên
A2. Hệ thống hiển thị thông tin chi tiết giáo viên đã chọn
A3. Admin chọn môn học cần phân công từ danh sách môn học
A4. Admin nhấn nút "Phân công"
A5. Hệ thống kiểm tra giáo viên đã được phân công môn học này chưa
A6. Hệ thống xác thực vai trò của người dùng được chọn (phải là Teacher)
A7. Hệ thống tạo bản ghi phân công mới với thông tin:
    - UserID (ID giáo viên)
    - SubjectID (ID môn học)
    - SubjectName (Tên môn học)
    - CreatedBy (Admin thực hiện)
    - CreatedAt (Thời gian tạo)
A8. Hệ thống lưu thông tin phân công vào cơ sở dữ liệu
A9. Hệ thống hiển thị thông báo "Phân công giáo viên thành công!"
A10. Hệ thống cập nhật lại danh sách phân công hiện tại

### Dòng sự kiện B - Xóa phân công giáo viên

B1. Admin chọn phân công cần xóa từ danh sách phân công hiện tại
B2. Admin nhấn nút "Xóa phân công"
B3. Hệ thống hiển thị hộp thoại xác nhận với thông tin:
    - Tên giáo viên
    - Tên môn học
    - Cảnh báo về việc xóa phân công
B4. Admin xác nhận xóa phân công
B5. Hệ thống kiểm tra ràng buộc dữ liệu:
    - Giáo viên có đề thi đang được sử dụng cho môn học này không
    - Giáo viên có câu hỏi đã tạo cho môn học này không
B6. Hệ thống xóa bản ghi phân công khỏi cơ sở dữ liệu
B7. Hệ thống hiển thị thông báo "Hủy phân công thành công!"
B8. Hệ thống cập nhật lại danh sách phân công

### Dòng sự kiện C - Xem phân công theo giáo viên

C1. Admin chọn giáo viên từ danh sách
C2. Admin nhấn nút "Xem phân công"
C3. Hệ thống truy vấn tất cả môn học được phân công cho giáo viên đã chọn
C4. Hệ thống hiển thị danh sách môn học của giáo viên với thông tin:
    - Mã môn học
    - Tên môn học
    - Ngày phân công
    - Người thực hiện phân công
C5. Admin có thể in hoặc xuất danh sách phân công

## Dòng sự kiện phụ

- **Phân công trùng lặp**: Nếu giáo viên đã được phân công môn học, hệ thống hiển thị thông báo "Giáo viên đã được phân công môn học này!" và không thực hiện thao tác
- **Không phải giáo viên**: Nếu người dùng được chọn không có vai trò Teacher, hệ thống hiển thị thông báo "Chỉ có thể phân công cho giáo viên!" và từ chối thao tác
- **Ràng buộc dữ liệu khi xóa**: 
  - Nếu giáo viên có đề thi đang được sử dụng, hệ thống hiển thị thông báo "Không thể hủy phân công vì giáo viên có đề thi đang được sử dụng!"
  - Nếu giáo viên có câu hỏi cho môn học, hệ thống cảnh báo và yêu cầu xác nhận
- **Không có dữ liệu**: Nếu không có giáo viên hoặc môn học nào, hệ thống hiển thị thông báo tương ứng
- **Lỗi kết nối cơ sở dữ liệu**: Hệ thống hiển thị thông báo lỗi và cho phép thử lại
- **Hủy thao tác**: Admin có thể nhấn nút "Hủy" để hủy thao tác và quay về danh sách

## Điều kiện tiên quyết

- Admin đã đăng nhập vào hệ thống với quyền quản trị
- Hệ thống cơ sở dữ liệu đang hoạt động bình thường
- Có ít nhất một người dùng với vai trò Teacher trong hệ thống
- Có ít nhất một môn học trong hệ thống
- Admin có quyền truy cập chức năng "Phân công môn học cho giáo viên"

## Hậu điều kiện

- **Thành công**: 
  - Thông tin phân công được cập nhật chính xác trong bảng TeacherSubject
  - Giáo viên có quyền truy cập và quản lý môn học được phân công
  - Giáo viên có thể tạo câu hỏi và đề thi cho môn học được phân công
  - Danh sách phân công được làm mới và hiển thị chính xác
  - Thông báo kết quả thao tác được hiển thị cho Admin
- **Thất bại**: 
  - Không có thông tin phân công nào được thay đổi trong cơ sở dữ liệu
  - Thông báo lỗi được hiển thị cho Admin với mô tả cụ thể
  - Admin có thể thực hiện lại thao tác hoặc chọn phân công khác
  - Dữ liệu hiện tại trong hệ thống không bị ảnh hưởng

## Quy tắc nghiệp vụ

1. **Quan hệ phân công**:
   - Một giáo viên có thể được phân công nhiều môn học (1-n)
   - Một môn học có thể có nhiều giáo viên được phân công (1-n)
   - Quan hệ tổng thể là nhiều-nhiều (n-n) thông qua bảng TeacherSubject

2. **Ràng buộc vai trò**:
   - Chỉ người dùng có RoleID = "Teacher" mới có thể được phân công
   - Admin không thể phân công chính mình làm giáo viên
   - Student không thể được phân công làm giáo viên

3. **Ràng buộc duy nhất**:
   - Không được phân công trùng lặp (cùng UserID và SubjectID)
   - Mỗi bản ghi phân công phải có ID duy nhất

4. **Quyền truy cập sau phân công**:
   - Giáo viên chỉ có thể truy cập môn học được phân công
   - Giáo viên có thể tạo câu hỏi cho môn học được phân công
   - Giáo viên có thể tạo đề thi cho môn học được phân công
   - Giáo viên có thể xem báo cáo cho môn học được phân công

5. **Ràng buộc xóa**:
   - Không được xóa phân công nếu giáo viên có đề thi đang được sử dụng
   - Cảnh báo khi xóa phân công nếu giáo viên có câu hỏi cho môn học
   - Phải kiểm tra tất cả dữ liệu liên quan trước khi xóa

## Giao diện liên quan

- **frmTeacherSubject**: Form chính phân công môn học cho giáo viên
  - Hiển thị danh sách giáo viên và môn học
  - Hiển thị danh sách phân công hiện tại
  - Các nút thêm/xóa phân công
  - Chức năng xem phân công theo giáo viên

- **Các lớp nghiệp vụ liên quan**:
  - **BTeacherSubject**: Xử lý logic phân công giáo viên
    - `AddTeacherSubject(TeacherSubject teacherSubject)`: Thêm phân công mới
    - `DeleteTeacherSubject(int id)`: Xóa phân công
    - `GetByTeacher(int userId)`: Lấy môn học của giáo viên
    - `IsTeacherAssigned(int userId, string subjectId)`: Kiểm tra phân công tồn tại
  
  - **BUserAccount**: Lấy danh sách giáo viên
    - `GetByRole(string roleId)`: Lấy danh sách theo vai trò Teacher
  
  - **BSubject**: Lấy danh sách môn học
    - `GetAll()`: Lấy tất cả môn học

## Luồng dữ liệu

1. **Input**: UserID (giáo viên), SubjectID (môn học), Admin thực hiện
2. **Processing**: Kiểm tra ràng buộc, tạo bản ghi TeacherSubject
3. **Output**: Thông báo kết quả, cập nhật danh sách phân công
4. **Storage**: Lưu vào bảng TeacherSubject trong cơ sở dữ liệu
