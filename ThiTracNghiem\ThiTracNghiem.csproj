﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{12F3B478-2FE3-4542-934E-BADF016017DC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>ThiTracNghiem</RootNamespace>
    <AssemblyName>ThiTracNghiem</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CrystalDecisions.CrystalReports.Engine, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.ReportSource, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="CrystalDecisions.Shared, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304" />
    <Reference Include="CrystalDecisions.Windows.Forms, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="DevComponents.DotNetBar2, Version=14.1.0.37, Culture=neutral, PublicKeyToken=7eb7c3a35b91de04, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Charts.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.CodeParser.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.Desktop.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.DataAccess.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.DataAccess.v24.1.UI, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Diagram.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Images.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Office.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v24.1.Drawing, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.PivotGrid.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.RichEdit.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v24.1.Export, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v24.1.UI, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraBars.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v24.1.Extensions, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v24.1.Wizard, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraDiagram.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraEditors.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGauges.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraLayout.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraNavBar.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraPivotGrid.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraPrinting.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v24.1.CodeCompletion, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v24.1.Extensions, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraRichEdit.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraScheduler.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraScheduler.v24.1.Core, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraScheduler.v24.1.Core.Desktop, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraTreeList.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraVerticalGrid.v24.1, Version=24.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="EPPlus, Version=7.5.1.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>packages\EPPlus.7.5.1\lib\net462\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.Interfaces, Version=7.5.0.0, Culture=neutral, PublicKeyToken=a694d7f3b0907a61, processorArchitecture=MSIL">
      <HintPath>packages\EPPlus.Interfaces.7.5.0\lib\net462\EPPlus.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus.System.Drawing, Version=7.5.0.0, Culture=neutral, PublicKeyToken=2308d35469c9bac0, processorArchitecture=MSIL">
      <HintPath>packages\EPPlus.System.Drawing.7.5.0\lib\net462\EPPlus.System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="Guna.UI2, Version=2.0.4.6, Culture=neutral, PublicKeyToken=8b9d14aa5142e261, processorArchitecture=MSIL">
      <HintPath>packages\Guna.UI2.WinForms.2.0.4.6\lib\net48\Guna.UI2.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=3.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.IO.RecyclableMemoryStream.3.0.1\lib\netstandard2.0\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1652.0\lib\net40\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1652.0\lib\net40\Microsoft.ReportViewer.DataVisualization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Design, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1652.0\lib\net40\Microsoft.ReportViewer.Design.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1652.0\lib\net40\Microsoft.ReportViewer.ProcessingObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1652.0\lib\net40\Microsoft.ReportViewer.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.SqlServer.Types.14.0.314.76\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing.Common, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Drawing.Common.8.0.4\lib\net462\System.Drawing.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.4.5.3\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\ExamSessionManager.cs" />
    <Compile Include="Common\ReportHelper.cs" />
    <Compile Include="Common\Session.cs" />
    <Compile Include="frmApproveExam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="frmApproveExam.Designer.cs">
      <DependentUpon>frmApproveExam.cs</DependentUpon>
    </Compile>
    <Compile Include="frmBackUp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmBackUp.Designer.cs">
      <DependentUpon>frmBackUp.cs</DependentUpon>
    </Compile>
    <Compile Include="frmCreateExam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="frmCreateExam.Designer.cs">
      <DependentUpon>frmCreateExam.cs</DependentUpon>
    </Compile>
    <Compile Include="frmExamDetail.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmExamDetail.Designer.cs">
      <DependentUpon>frmExamDetail.cs</DependentUpon>
    </Compile>
    <Compile Include="frmEditExamSession.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmEditExamSession.Designer.cs">
      <DependentUpon>frmEditExamSession.cs</DependentUpon>
    </Compile>
    <Compile Include="frmLoad.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLoad.Designer.cs">
      <DependentUpon>frmLoad.cs</DependentUpon>
    </Compile>
    <Compile Include="frmManageExamSession.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="frmManageExamSession.Designer.cs">
      <DependentUpon>frmManageExamSession.cs</DependentUpon>
    </Compile>
    <Compile Include="frmManageUserSubject.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="frmManageUserSubject.Designer.cs">
      <DependentUpon>frmManageUserSubject.cs</DependentUpon>
    </Compile>
    <Compile Include="frmRestore.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmRestore.Designer.cs">
      <DependentUpon>frmRestore.cs</DependentUpon>
    </Compile>
    <Compile Include="frmTeacherSubject.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="frmTeacherSubject.Designer.cs">
      <DependentUpon>frmTeacherSubject.cs</DependentUpon>
    </Compile>
    <Compile Include="newFrmMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="newFrmMain.Designer.cs">
      <DependentUpon>newFrmMain.cs</DependentUpon>
    </Compile>
    <Compile Include="frmChangePassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangePassword.Designer.cs">
      <DependentUpon>frmChangePassword.cs</DependentUpon>
    </Compile>
    <Compile Include="frmLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLogin.Designer.cs">
      <DependentUpon>frmLogin.cs</DependentUpon>
    </Compile>
    <Compile Include="frmManageQuestion.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="frmManageQuestion.Designer.cs">
      <DependentUpon>frmManageQuestion.cs</DependentUpon>
    </Compile>
    <Compile Include="frmOption.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmOption.Designer.cs">
      <DependentUpon>frmOption.cs</DependentUpon>
    </Compile>
    <Compile Include="frmTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTest.Designer.cs">
      <DependentUpon>frmTest.cs</DependentUpon>
    </Compile>
    <Compile Include="frmLeaderBoard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLeaderBoard.Designer.cs">
      <DependentUpon>frmLeaderBoard.cs</DependentUpon>
    </Compile>
    <Compile Include="frmTestResult.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTestResult.Designer.cs">
      <DependentUpon>frmTestResult.cs</DependentUpon>
    </Compile>
    <Compile Include="frm_ManageSubject.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="frm_ManageSubject.Designer.cs">
      <DependentUpon>frm_ManageSubject.cs</DependentUpon>
    </Compile>
    <Compile Include="frmManageUser.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="frmManageUser.Designer.cs">
      <DependentUpon>frmManageUser.cs</DependentUpon>
    </Compile>
    <Compile Include="NewFrmReportBySubject.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="NewFrmReportBySubject.Designer.cs">
      <DependentUpon>NewFrmReportBySubject.cs</DependentUpon>
    </Compile>
    <Compile Include="NewFrmReportByTime.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="NewFrmReportByTime.Designer.cs">
      <DependentUpon>NewFrmReportByTime.cs</DependentUpon>
    </Compile>
    <Compile Include="NewFrmReportExamSession.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="NewFrmReportExamSession.Designer.cs">
      <DependentUpon>NewFrmReportExamSession.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="frmForgotPassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmForgotPassword.Designer.cs">
      <DependentUpon>frmForgotPassword.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="QL_THITRACNGHIEM2DataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>QL_THITRACNGHIEM2DataSet.xsd</DependentUpon>
    </Compile>
    <EmbeddedResource Include="frmApproveExam.resx">
      <DependentUpon>frmApproveExam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmBackUp.resx">
      <DependentUpon>frmBackUp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCreateExam.resx">
      <DependentUpon>frmCreateExam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmExamDetail.resx">
      <DependentUpon>frmExamDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmEditExamSession.resx">
      <DependentUpon>frmEditExamSession.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmForgotPassword.resx">
      <DependentUpon>frmForgotPassword.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLoad.resx">
      <DependentUpon>frmLoad.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmManageExamSession.resx">
      <DependentUpon>frmManageExamSession.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmManageUserSubject.resx">
      <DependentUpon>frmManageUserSubject.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmRestore.resx">
      <DependentUpon>frmRestore.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTeacherSubject.resx">
      <DependentUpon>frmTeacherSubject.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="newFrmMain.resx">
      <DependentUpon>newFrmMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmChangePassword.resx">
      <DependentUpon>frmChangePassword.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLogin.resx">
      <DependentUpon>frmLogin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmManageQuestion.resx">
      <DependentUpon>frmManageQuestion.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmOption.resx">
      <DependentUpon>frmOption.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTest.resx">
      <DependentUpon>frmTest.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmLeaderBoard.resx">
      <DependentUpon>frmLeaderBoard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmTestResult.resx">
      <DependentUpon>frmTestResult.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frm_ManageSubject.resx">
      <DependentUpon>frm_ManageSubject.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmManageUser.resx">
      <DependentUpon>frmManageUser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NewFrmReportBySubject.resx">
      <DependentUpon>NewFrmReportBySubject.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NewFrmReportByTime.resx">
      <DependentUpon>NewFrmReportByTime.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NewFrmReportExamSession.resx">
      <DependentUpon>NewFrmReportExamSession.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Reports\ExamSessionReport.rdlc">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\SubjectReport.rdlc">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\TimeReport.rdlc">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BusinessLogicLayer\BusinessLogicLayer.csproj">
      <Project>{020b16b4-7264-4e82-ac52-da80a39f7b0c}</Project>
      <Name>BusinessLogicLayer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Entities\Entities.csproj">
      <Project>{2b1d538b-bbbc-4903-9f25-f380e70d87e7}</Project>
      <Name>Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\TestCore\TestCore.csproj">
      <Project>{688b809c-23fe-464e-9a52-d9402b50a3cd}</Project>
      <Name>TestCore</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <None Include="QL_THITRACNGHIEM2DataSet.xsc">
      <DependentUpon>QL_THITRACNGHIEM2DataSet.xsd</DependentUpon>
    </None>
    <None Include="QL_THITRACNGHIEM2DataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>QL_THITRACNGHIEM2DataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="QL_THITRACNGHIEM2DataSet.xss">
      <DependentUpon>QL_THITRACNGHIEM2DataSet.xsd</DependentUpon>
    </None>
    <None Include="Resources\profile-avatar.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lock.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lock - Copy.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\profile-avatar - copy.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ranking-factor.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Search.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\restore.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cloud backup.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Planet.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\hello.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Security Research.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\option.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\result_rank.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ranking-factor1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\11317256.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\11487212.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Backup_Database.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\mail.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\No-Image.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\toolxox.com-iscout-6nje7zxhVN_upscayl_4x_ultramix_balanced.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\eye.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\closeeye.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\add.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\search_grey.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\upload_img.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\closess.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\minus.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\heelo.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Cat2.gif" />
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>