@startuml PhanCong_UseCaseDiagram

!theme plain
skinparam actorStyle awesome
skinparam usecase {
    BackgroundColor LightBlue
    BorderColor DarkBlue
    FontSize 10
}
skinparam rectangle {
    BackgroundColor LightYellow
    BorderColor Orange
    FontStyle bold
}

title Sơ đồ Use Case - Phân công Giáo viên và Học viên

' Actors
actor "Admin\n(Quản trị viên)" as admin

rectangle "Hệ thống Phân công" {

    ' Authentication Package
    package "Xác thực" {
        usecase "Đăng nhập" as login
        usecase "Đăng xuất" as logout
        usecase "<PERSON><PERSON><PERSON> thực quyền quản trị" as checkadmin
    }

    ' Assignment Management Package - Main Focus
    package "Quản lý Phân công" {
        usecase "Phân công giáo viên và học viên" as manageassignment
        usecase "Phân công giáo viên" as assignteacher
        usecase "<PERSON>ân công học viên cho môn học" as assignstudent
        usecase "Hủy phân công giáo viên" as removeteacher
        usecase "H<PERSON><PERSON> gán môn học học viên" as removestudent
        usecase "Xem thông tin phân công" as viewassignment
    }

    ' Selection Package
    package "Lựa chọn Dữ liệu" {
        usecase "Chọn giáo viên" as selectteacher
        usecase "Chọn học viên" as selectstudent
        usecase "Chọn môn học" as selectsubject
        usecase "Lấy danh sách giáo viên" as getteachers
        usecase "Lấy danh sách học viên" as getstudents
        usecase "Lấy danh sách môn học" as getsubjects
    }

    ' Validation Package
    package "Xác thực Dữ liệu" {
        usecase "Kiểm tra phân công tồn tại" as checkassignment
        usecase "Kiểm tra ràng buộc dữ liệu" as checkconstraints
        usecase "Xác thực vai trò người dùng" as checkrole
    }

    ' Data Processing Package
    package "Xử lý Dữ liệu" {
        usecase "Lưu thông tin phân công" as saveassignment
        usecase "Xóa thông tin phân công" as deleteassignment
        usecase "Cập nhật danh sách phân công" as updatelist
        usecase "Hiển thị thông báo kết quả" as showmessage
    }

}

' Actor-UseCase Relationships

' Authentication
admin --> login
admin --> logout

' Main Assignment Management Use Cases
admin --> manageassignment
admin --> assignteacher
admin --> assignstudent
admin --> removeteacher
admin --> removestudent
admin --> viewassignment

' Selection Use Cases
admin --> selectteacher
admin --> selectstudent
admin --> selectsubject
admin --> getteachers
admin --> getstudents
admin --> getsubjects

' Validation Use Cases
admin --> checkassignment
admin --> checkconstraints
admin --> checkrole

' Data Processing Use Cases
admin --> saveassignment
admin --> deleteassignment
admin --> updatelist
admin --> showmessage

' Include relationships
manageassignment ..> checkadmin : <<include>>
manageassignment ..> getteachers : <<include>>
manageassignment ..> getstudents : <<include>>
manageassignment ..> getsubjects : <<include>>

assignteacher ..> selectteacher : <<include>>
assignteacher ..> selectsubject : <<include>>
assignteacher ..> checkrole : <<include>>
assignteacher ..> checkassignment : <<include>>
assignteacher ..> saveassignment : <<include>>
assignteacher ..> updatelist : <<include>>
assignteacher ..> showmessage : <<include>>

assignstudent ..> selectstudent : <<include>>
assignstudent ..> selectsubject : <<include>>
assignstudent ..> checkrole : <<include>>
assignstudent ..> checkassignment : <<include>>
assignstudent ..> saveassignment : <<include>>
assignstudent ..> updatelist : <<include>>
assignstudent ..> showmessage : <<include>>

removeteacher ..> checkconstraints : <<include>>
removeteacher ..> deleteassignment : <<include>>
removeteacher ..> updatelist : <<include>>
removeteacher ..> showmessage : <<include>>

removestudent ..> checkconstraints : <<include>>
removestudent ..> deleteassignment : <<include>>
removestudent ..> updatelist : <<include>>
removestudent ..> showmessage : <<include>>

viewassignment ..> getteachers : <<include>>
viewassignment ..> getstudents : <<include>>
viewassignment ..> getsubjects : <<include>>

' Extend relationships
manageassignment ..> assignteacher : <<extend>>
manageassignment ..> assignstudent : <<extend>>
manageassignment ..> viewassignment : <<extend>>
assignteacher ..> removeteacher : <<extend>>
assignstudent ..> removestudent : <<extend>>

@enduml
