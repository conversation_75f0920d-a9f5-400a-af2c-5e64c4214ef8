# Đặc tả Use Case - Quản lý Môn học

## Thông tin chung

| Mục | Mô tả |
|-----|-------|
| **Tên use case** | Quản lý môn học |
| **Tóm tắt** | Use case mô tả quá trình quản trị viên thực hiện các thao tác quản lý thông tin môn học trong hệ thống |
| **Tác nhân** | Admin (Quản trị viên) |
| **Use case liên quan** | - Xem danh sách môn học (include)<br/>- <PERSON><PERSON><PERSON> thực thông tin môn học (include)<br/>- <PERSON>ểm tra trùng lặp (include)<br/>- Kiểm tra tồn tại môn học (include) |

## Dòng sự kiện chính

1. <PERSON><PERSON> đăng nhập vào hệ thống
2. <PERSON><PERSON> thống hiển thị màn hình chính với menu quản lý
3. <PERSON><PERSON> chọn chức năng "Quản lý môn học" từ menu
4. <PERSON><PERSON> thống hiển thị danh sách tất cả môn học hiện có
5. Admin chọn thao tác muốn thực hiện:
   - **Thêm môn học mới**: Chuyển đến dòng sự kiện A
   - **Sửa thông tin môn học**: Chuyển đến dòng sự kiện B  
   - **Xóa môn học**: Chuyển đến dòng sự kiện C
   - **Tìm kiếm môn học**: Chuyển đến dòng sự kiện D

### Dòng sự kiện A - Thêm môn học mới

A1. Admin nhấn nút "Thêm mới"
A2. Hệ thống hiển thị form nhập thông tin môn học
A3. Admin nhập thông tin môn học:
    - Mã môn học (SubjectId)
    - Tên môn học (SubjectName)  
    - Mô tả (Description)
A4. Admin nhấn nút "Lưu"
A5. Hệ thống xác thực thông tin đã nhập
A6. Hệ thống kiểm tra trùng lặp mã môn học và tên môn học
A7. Hệ thống lưu thông tin môn học mới vào cơ sở dữ liệu
A8. Hệ thống hiển thị thông báo "Thêm mới môn học thành công!"
A9. Hệ thống cập nhật lại danh sách môn học

### Dòng sự kiện B - Sửa thông tin môn học

B1. Admin chọn môn học cần sửa từ danh sách
B2. Admin nhấn nút "Sửa"
B3. Hệ thống hiển thị form chỉnh sửa với thông tin hiện tại của môn học
B4. Admin chỉnh sửa thông tin cần thiết
B5. Admin nhấn nút "Lưu"
B6. Hệ thống xác thực thông tin đã nhập
B7. Hệ thống kiểm tra tồn tại môn học
B8. Hệ thống cập nhật thông tin môn học trong cơ sở dữ liệu
B9. Hệ thống hiển thị thông báo "Cập nhật thông tin môn học thành công!"
B10. Hệ thống cập nhật lại danh sách môn học

### Dòng sự kiện C - Xóa môn học

C1. Admin chọn môn học cần xóa từ danh sách
C2. Admin nhấn nút "Xóa"
C3. Hệ thống hiển thị hộp thoại xác nhận xóa
C4. Admin xác nhận xóa
C5. Hệ thống kiểm tra tồn tại môn học
C6. Hệ thống kiểm tra ràng buộc dữ liệu (môn học có đang được sử dụng không)
C7. Hệ thống xóa môn học khỏi cơ sở dữ liệu
C8. Hệ thống hiển thị thông báo "Xóa môn học thành công!"
C9. Hệ thống cập nhật lại danh sách môn học

### Dòng sự kiện D - Tìm kiếm môn học

D1. Admin nhập từ khóa tìm kiếm vào ô tìm kiếm
D2. Admin nhấn nút "Tìm kiếm" hoặc nhấn Enter
D3. Hệ thống thực hiện tìm kiếm theo từ khóa trong mã môn học và tên môn học
D4. Hệ thống hiển thị danh sách môn học phù hợp với từ khóa
D5. Nếu không tìm thấy, hệ thống hiển thị thông báo "Không tìm thấy môn học phù hợp"

## Dòng sự kiện phụ

- **Lỗi xác thực dữ liệu**: Nếu thông tin nhập vào không hợp lệ (thiếu thông tin bắt buộc, định dạng sai), hệ thống hiển thị thông báo lỗi và yêu cầu nhập lại
- **Trùng lặp dữ liệu**: Nếu mã môn học hoặc tên môn học đã tồn tại, hệ thống hiển thị thông báo "Mã môn học hoặc tên môn học đã tồn tại!" và yêu cầu nhập lại
- **Môn học không tồn tại**: Nếu môn học cần sửa/xóa không tồn tại, hệ thống hiển thị thông báo lỗi
- **Ràng buộc dữ liệu**: Nếu môn học đang được sử dụng (có câu hỏi, đề thi, hoặc được phân công), hệ thống không cho phép xóa và hiển thị thông báo
- **Hủy thao tác**: Admin có thể nhấn nút "Hủy" để hủy thao tác đang thực hiện và quay về danh sách môn học

## Điều kiện tiên quyết

- Admin đã đăng nhập vào hệ thống với quyền quản trị
- Hệ thống cơ sở dữ liệu đang hoạt động bình thường
- Admin có quyền truy cập chức năng "Quản lý môn học"

## Hậu điều kiện

- **Thành công**: 
  - Thông tin môn học được cập nhật trong cơ sở dữ liệu
  - Danh sách môn học được làm mới và hiển thị chính xác
  - Thông báo kết quả thao tác được hiển thị cho Admin
- **Thất bại**: 
  - Dữ liệu trong cơ sở dữ liệu không thay đổi
  - Thông báo lỗi được hiển thị cho Admin
  - Admin có thể thực hiện lại thao tác

## Quy tắc nghiệp vụ

1. **Mã môn học**: Bắt buộc, không được trùng lặp, định dạng chuỗi
2. **Tên môn học**: Bắt buộc, không được trùng lặp
3. **Mô tả**: Tùy chọn, có thể để trống
4. **Quyền truy cập**: Chỉ Admin mới có quyền thực hiện các thao tác quản lý môn học
5. **Ràng buộc xóa**: Không được xóa môn học đang có dữ liệu liên quan (câu hỏi, đề thi, phân công)

## Giao diện liên quan

- **frm_ManageSubject**: Form chính quản lý môn học
- **Các control chính**:
  - `grv_DataUser`: DataGridView hiển thị danh sách môn học
  - `txt_SubjectId`: TextBox nhập mã môn học
  - `txt_SubjectName`: TextBox nhập tên môn học  
  - `txt_SubjectDesb`: TextBox nhập mô tả môn học
  - `btn_Add`, `btn_Edit`, `btn_Delete`: Các nút thao tác
  - `btn_Search`: Nút tìm kiếm
  - `txt_Search`: TextBox nhập từ khóa tìm kiếm
