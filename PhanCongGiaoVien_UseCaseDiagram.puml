@startuml PhanCongGiaoVien_UseCaseDiagram

!theme plain
skinparam actorStyle awesome
skinparam usecase {
    BackgroundColor LightBlue
    BorderColor DarkBlue
    FontSize 10
}
skinparam rectangle {
    BackgroundColor LightYellow
    BorderColor Orange
    FontStyle bold
}

title Sơ đồ Use Case - Phân công Giáo viên

' Actors
actor "Admin\n(Quản trị viên)" as admin

rectangle "Hệ thống Phân công Giáo viên" {

    ' Authentication Package
    package "Xác thực" {
        usecase "Đăng nhập" as login
        usecase "Đăng xuất" as logout
        usecase "<PERSON><PERSON><PERSON> thực quyền quản trị" as checkadmin
    }

    ' Teacher Assignment Management Package - Main Focus
    package "Quản lý Phân công Giáo viên" {
        usecase "Phân công giáo viên cho môn học" as assignteacher
        usecase "Thêm phân công giáo viên" as addassignment
        usecase "Xóa phân công giáo viên" as removeassignment
        usecase "Xem phân công theo giáo viên" as viewbyteacher
    }

    ' Selection Package
    package "Lựa chọn Dữ liệu" {
        usecase "Chọn giáo viên" as selectteacher
        usecase "Chọn môn học" as selectsubject
        usecase "Lấy danh sách giáo viên" as getteachers
        usecase "Lấy danh sách môn học" as getsubjects
        usecase "Lấy danh sách phân công" as getassignments
    }

    ' Validation Package
    package "Xác thực Dữ liệu" {
        usecase "Kiểm tra phân công tồn tại" as checkassignment
        usecase "Kiểm tra ràng buộc dữ liệu" as checkconstraints
        usecase "Xác thực vai trò giáo viên" as checkteacherrole
    }

    ' Data Processing Package
    package "Xử lý Dữ liệu" {
        usecase "Lưu thông tin phân công" as saveassignment
        usecase "Xóa thông tin phân công" as deleteassignment
        usecase "Cập nhật danh sách phân công" as updatelist
        usecase "Hiển thị thông báo kết quả" as showmessage
    }

}

' Actor-UseCase Relationships

' Authentication
admin --> login
admin --> logout

' Main Teacher Assignment Management Use Cases
admin --> assignteacher
admin --> addassignment
admin --> removeassignment
admin --> viewbyteacher

' Selection Use Cases
admin --> selectteacher
admin --> selectsubject
admin --> getteachers
admin --> getsubjects
admin --> getassignments

' Validation Use Cases
admin --> checkassignment
admin --> checkconstraints
admin --> checkteacherrole

' Data Processing Use Cases
admin --> saveassignment
admin --> deleteassignment
admin --> updatelist
admin --> showmessage

' Include relationships
assignteacher ..> checkadmin : <<include>>
assignteacher ..> getteachers : <<include>>
assignteacher ..> getsubjects : <<include>>
assignteacher ..> getassignments : <<include>>

addassignment ..> selectteacher : <<include>>
addassignment ..> selectsubject : <<include>>
addassignment ..> checkteacherrole : <<include>>
addassignment ..> checkassignment : <<include>>
addassignment ..> saveassignment : <<include>>
addassignment ..> updatelist : <<include>>
addassignment ..> showmessage : <<include>>

removeassignment ..> checkconstraints : <<include>>
removeassignment ..> deleteassignment : <<include>>
removeassignment ..> updatelist : <<include>>
removeassignment ..> showmessage : <<include>>

viewbyteacher ..> selectteacher : <<include>>
viewbyteacher ..> getassignments : <<include>>

' Extend relationships
assignteacher ..> addassignment : <<extend>>
assignteacher ..> removeassignment : <<extend>>
assignteacher ..> viewbyteacher : <<extend>>

@enduml
