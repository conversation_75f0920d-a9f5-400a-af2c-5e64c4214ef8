@startuml ReportManagement_ClassDiagram

!theme plain
skinparam classAttributeIconSize 0
skinparam classFontSize 10
skinparam classAttributeFontSize 9
skinparam classOperationFontSize 9

title Sơ đồ lớp - <PERSON><PERSON><PERSON> năng B<PERSON>o cáo

class TestHistory {
    +TestId: int
    +UserId: int
    +SubjectID: string
    +TestDate: DateTime
    +CorrectAnswer: int
    +TotalQuestion: int
    +Mark: int
    +CreatedBy: string
    +ModifiedBy: string
    +CreatedAt: DateTime
    +ModifiedAt: DateTime
    --
    +SaveResult(testHistory: TestHistory): void
    +GetLeaderBoard(testHistory: TestHistory): DataTable
    +GetReportBySubject(subjectId: string): DataTable
    +GetReportByTime(startDate: DateTime, endDate: DateTime): DataTable
}

class ExamSession {
    +SessionID: int
    +SessionName: string
    +StartTime: DateTime
    +EndTime: DateTime
    +Status: string
    +CreatedBy: string
    +CreatedAt: DateTime
    +ModifiedBy: string
    +ModifiedAt: DateTime
    --
    +AddExamSession(examSession: ExamSession): int
    +UpdateStatus(sessionId: int, status: string, modifiedBy: string): void
    +UpdateExamSession(examSession: ExamSession): void
    +GetAll(): DataTable
    +GetCurrent(): DataTable
    +GetByUser(userId: int): DataTable
    +GetById(sessionId: int): ExamSession
}

class UserExamSession {
    +UserSessionID: int
    +UserID: int
    +Username: string
    +Fullname: string
    +SessionID: int
    +Status: string
    +StartTime: DateTime?
    +EndTime: DateTime?
    +CreatedBy: string
    +CreatedAt: DateTime
    +ModifiedBy: string
    +ModifiedAt: DateTime
    --
    +AddUserExamSession(userExamSession: UserExamSession): void
    +UpdateStatus(userId: int, sessionId: int, status: string, startTime: DateTime?, endTime: DateTime?, modifiedBy: string): void
    +GetBySession(sessionId: int): DataTable
    +GetByUserAndSession(userId: int, sessionId: int): UserExamSession
    +GetStatistics(sessionId: int): DataTable
}

class Subject {
    +SubjectId: string
    +SubjectName: string
    +Description: string
    +CreatedBy: string
    +ModifiedBy: string
    +CreatedAt: DateTime
    +ModifiedAt: DateTime
    --
    +AddNewSubject(newSubject: Subject): void
    +GetAll(): DataTable
    +UpdateSubject(editSubject: Subject): void
    +DeleteSubject(subjectId: string): void
    +Search(keyword: string): DataTable
    +IsSubjectExist(subjectId: string): bool
}

' Relationships
TestHistory }o--|| Subject : "n-1"
UserExamSession }o--|| ExamSession : "n-1"

@enduml
