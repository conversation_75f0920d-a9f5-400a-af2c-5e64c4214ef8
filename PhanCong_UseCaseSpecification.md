# Đặc tả Use Case - <PERSON><PERSON> công Giáo viên và Học viên

## Thông tin chung

| Mục | <PERSON>ô tả |
|-----|-------|
| **Tên use case** | Phân công giáo viên và học viên cho môn học |
| **Tóm tắt** | Use case mô tả quá trình quản trị viên thực hiện phân công giáo viên giảng dạy và học viên tham gia các môn học trong hệ thống |
| **Tác nhân** | Admin (Quản trị viên) |
| **Use case liên quan** | - L<PERSON>y danh sách người dùng (include)<br/>- <PERSON><PERSON><PERSON> danh sách môn học (include)<br/>- Kiểm tra phân công tồn tại (include)<br/>- <PERSON><PERSON><PERSON> thực quyền truy cập (include) |

## Dòng sự kiện chính

1. <PERSON><PERSON> đăng nhập vào hệ thống
2. <PERSON><PERSON> thống xác thực quyền quản trị của Admin
3. Hệ thống hiển thị menu quản lý với các tùy chọn phân công
4. Admin chọn loại phân công muốn thực hiện:
   - **Phân công giáo viên**: Chuyển đến dòng sự kiện A
   - **Phân công học viên cho môn học**: Chuyển đến dòng sự kiện B

### Dòng sự kiện A - Phân công giáo viên

A1. Admin chọn "Phân công môn học cho giáo viên"
A2. Hệ thống hiển thị giao diện phân công giáo viên với:
    - Danh sách tất cả giáo viên (vai trò Teacher)
    - Danh sách tất cả môn học
    - Danh sách phân công hiện tại
A3. Admin chọn giáo viên từ danh sách
A4. Admin chọn môn học cần phân công từ danh sách
A5. Admin nhấn nút "Phân công"
A6. Hệ thống kiểm tra giáo viên đã được phân công môn học này chưa
A7. Hệ thống lưu thông tin phân công vào cơ sở dữ liệu
A8. Hệ thống hiển thị thông báo "Phân công giáo viên thành công!"
A9. Hệ thống cập nhật lại danh sách phân công hiện tại

### Dòng sự kiện A2 - Hủy phân công giáo viên

A2.1. Admin chọn phân công cần hủy từ danh sách phân công hiện tại
A2.2. Admin nhấn nút "Hủy phân công"
A2.3. Hệ thống hiển thị hộp thoại xác nhận hủy phân công
A2.4. Admin xác nhận hủy phân công
A2.5. Hệ thống kiểm tra ràng buộc dữ liệu (giáo viên có đề thi đang sử dụng không)
A2.6. Hệ thống xóa thông tin phân công khỏi cơ sở dữ liệu
A2.7. Hệ thống hiển thị thông báo "Hủy phân công thành công!"
A2.8. Hệ thống cập nhật lại danh sách phân công

### Dòng sự kiện B - Phân công học viên cho môn học

B1. Admin chọn "Gán môn học cho người thi"
B2. Hệ thống hiển thị giao diện phân công học viên với:
    - Danh sách tất cả học viên (vai trò User/Student)
    - Danh sách tất cả môn học
    - Danh sách môn học đã được gán cho từng học viên
B3. Admin chọn học viên từ danh sách
B4. Hệ thống hiển thị danh sách môn học chưa được gán cho học viên đã chọn
B5. Admin chọn môn học cần gán từ danh sách
B6. Admin nhấn nút "Gán môn học"
B7. Hệ thống kiểm tra học viên đã được gán môn học này chưa
B8. Hệ thống lưu thông tin gán môn học vào cơ sở dữ liệu
B9. Hệ thống hiển thị thông báo "Gán môn học thành công!"
B10. Hệ thống cập nhật lại danh sách môn học của học viên

### Dòng sự kiện B2 - Hủy gán môn học cho học viên

B2.1. Admin chọn học viên từ danh sách
B2.2. Hệ thống hiển thị danh sách môn học đã được gán cho học viên
B2.3. Admin chọn môn học cần hủy gán
B2.4. Admin nhấn nút "Hủy gán"
B2.5. Hệ thống hiển thị hộp thoại xác nhận hủy gán môn học
B2.6. Admin xác nhận hủy gán
B2.7. Hệ thống kiểm tra ràng buộc dữ liệu (học viên có lịch sử thi môn này không)
B2.8. Hệ thống xóa thông tin gán môn học khỏi cơ sở dữ liệu
B2.9. Hệ thống hiển thị thông báo "Hủy gán môn học thành công!"
B2.10. Hệ thống cập nhật lại danh sách môn học của học viên

### Dòng sự kiện C - Xem thông tin phân công

C1. Admin chọn "Xem thông tin phân công"
C2. Hệ thống hiển thị báo cáo tổng quan phân công:
    - Danh sách giáo viên và môn học được phân công
    - Danh sách học viên và môn học được gán
    - Thống kê số lượng phân công
C3. Admin có thể lọc thông tin theo:
    - Theo giáo viên cụ thể
    - Theo môn học cụ thể
    - Theo học viên cụ thể

## Dòng sự kiện phụ

- **Phân công trùng lặp**: Nếu giáo viên đã được phân công môn học hoặc học viên đã được gán môn học, hệ thống hiển thị thông báo "Đã được phân công/gán trước đó!" và không thực hiện thao tác
- **Không có dữ liệu**: Nếu không có giáo viên/học viên/môn học nào trong hệ thống, hệ thống hiển thị thông báo tương ứng
- **Ràng buộc dữ liệu khi hủy**: 
  - Nếu giáo viên có đề thi đang được sử dụng, không cho phép hủy phân công
  - Nếu học viên có lịch sử thi môn học, cảnh báo trước khi hủy gán
- **Lỗi kết nối cơ sở dữ liệu**: Nếu không thể kết nối cơ sở dữ liệu, hệ thống hiển thị thông báo lỗi
- **Hủy thao tác**: Admin có thể hủy thao tác bất kỳ lúc nào và quay về menu chính

## Điều kiện tiên quyết

- Admin đã đăng nhập vào hệ thống với quyền quản trị
- Hệ thống cơ sở dữ liệu đang hoạt động bình thường
- Có ít nhất một giáo viên (vai trò Teacher) trong hệ thống để phân công
- Có ít nhất một học viên (vai trò User/Student) trong hệ thống để gán môn học
- Có ít nhất một môn học trong hệ thống để thực hiện phân công
- Admin có quyền truy cập chức năng "Phân công môn học" và "Gán môn học cho người thi"

## Hậu điều kiện

- **Thành công**: 
  - Thông tin phân công được cập nhật chính xác trong cơ sở dữ liệu
  - Giáo viên có quyền truy cập và quản lý môn học được phân công
  - Học viên có thể tham gia thi các môn học đã được gán
  - Danh sách phân công được làm mới và hiển thị chính xác
  - Thông báo kết quả thao tác được hiển thị cho Admin
- **Thất bại**: 
  - Không có thông tin phân công nào được thay đổi trong cơ sở dữ liệu
  - Thông báo lỗi được hiển thị cho Admin với mô tả cụ thể
  - Admin có thể thực hiện lại thao tác hoặc chọn phân công khác
  - Dữ liệu hiện tại trong hệ thống không bị ảnh hưởng

## Quy tắc nghiệp vụ

1. **Phân công giáo viên**:
   - Một giáo viên có thể được phân công nhiều môn học
   - Một môn học có thể có nhiều giáo viên được phân công
   - Không được phân công trùng lặp (cùng giáo viên, cùng môn học)
   - Chỉ người dùng có vai trò "Teacher" mới có thể được phân công

2. **Gán môn học cho học viên**:
   - Một học viên có thể được gán nhiều môn học
   - Một môn học có thể được gán cho nhiều học viên
   - Không được gán trùng lặp (cùng học viên, cùng môn học)
   - Chỉ người dùng có vai trò "User" hoặc "Student" mới có thể được gán môn học

3. **Quyền truy cập**:
   - Chỉ Admin mới có quyền thực hiện phân công và gán môn học
   - Giáo viên chỉ có thể truy cập môn học được phân công
   - Học viên chỉ có thể thi các môn học đã được gán

4. **Ràng buộc xóa**:
   - Không được hủy phân công giáo viên nếu có đề thi đang được sử dụng
   - Cảnh báo khi hủy gán môn học cho học viên đã có lịch sử thi

5. **Tính nhất quán dữ liệu**:
   - Phân công phải được đồng bộ với quyền truy cập trong hệ thống
   - Khi hủy phân công, cần kiểm tra và cập nhật các dữ liệu liên quan

## Giao diện liên quan

- **frmTeacherSubject**: Form phân công môn học cho giáo viên
  - Danh sách giáo viên và môn học được phân công
  - Nút thêm/xóa phân công
  - Kiểm tra phân công đã tồn tại

- **frmManageUserSubject**: Form gán môn học cho học viên
  - `grv_Users`: DataGridView hiển thị danh sách học viên
  - `grv_Subjects`: DataGridView hiển thị danh sách môn học
  - `grv_UserSubjects`: DataGridView hiển thị môn học đã gán cho học viên
  - `btn_Assign`: Nút gán môn học
  - `btn_Remove`: Nút hủy gán môn học

- **Các lớp nghiệp vụ liên quan**:
  - `BTeacherSubject`: Xử lý logic phân công giáo viên
    - `AddTeacherSubject()`: Thêm phân công
    - `DeleteTeacherSubject()`: Hủy phân công
    - `GetByTeacher()`: Lấy môn học của giáo viên
    - `IsTeacherAssigned()`: Kiểm tra phân công tồn tại
  
  - `BUserSubject`: Xử lý logic gán môn học cho học viên
    - `Insert()`: Gán môn học
    - `Delete()`: Hủy gán môn học
    - `GetByUser()`: Lấy môn học của học viên
    - `GetBySubject()`: Lấy học viên theo môn học
    - `GetAll()`: Lấy tất cả thông tin gán môn học
