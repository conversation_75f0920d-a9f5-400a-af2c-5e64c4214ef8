# Đặc tả Use Case - <PERSON><PERSON> công Học viên

## Thông tin chung

| Mục | <PERSON><PERSON> tả |
|-----|-------|
| **Tên use case** | G<PERSON> môn học cho học viên |
| **Tóm tắt** | Use case mô tả quá trình quản trị viên thực hiện gán môn học cho học viên để họ có thể tham gia thi trắc nghiệm |
| **Tác nhân** | Admin (Quản trị viên) |
| **Use case liên quan** | - <PERSON><PERSON><PERSON> danh sách học viên (include)<br/>- <PERSON><PERSON><PERSON> danh sách môn học (include)<br/>- Ki<PERSON>m tra gán môn học tồn tại (include)<br/>- <PERSON><PERSON><PERSON> thực quyền quản trị (include) |

## Dòng sự kiện chính

1. <PERSON><PERSON> đăng nhập vào hệ thống
2. <PERSON><PERSON> thống xác thực quyền quản trị của Admin
3. <PERSON><PERSON> chọn chức năng "<PERSON><PERSON> môn học cho người thi" từ menu
4. Hệ thống hiển thị giao diện gán môn học với:
   - Danh sách tất cả học viên (vai trò User/Student)
   - Danh sách tất cả môn học
   - Danh sách môn học đã được gán cho từng học viên
5. Admin thực hiện thao tác:
   - **Gán môn học cho học viên**: Chuyển đến dòng sự kiện A
   - **Hủy gán môn học**: Chuyển đến dòng sự kiện B
   - **Xem môn học của học viên**: Chuyển đến dòng sự kiện C

### Dòng sự kiện A - Gán môn học cho học viên

A1. Admin chọn học viên từ danh sách học viên
A2. Hệ thống hiển thị thông tin chi tiết học viên đã chọn
A3. Hệ thống hiển thị danh sách môn học chưa được gán cho học viên này
A4. Admin chọn môn học cần gán từ danh sách môn học khả dụng
A5. Admin nhấn nút "Gán môn học"
A6. Hệ thống kiểm tra học viên đã được gán môn học này chưa
A7. Hệ thống xác thực vai trò của người dùng được chọn (phải là User hoặc Student)
A8. Hệ thống tạo bản ghi gán môn học mới với thông tin:
    - UserID (ID học viên)
    - SubjectID (ID môn học)
    - Username (Tên đăng nhập học viên)
    - Fullname (Họ tên học viên)
    - SubjectName (Tên môn học)
    - CreatedBy (Admin thực hiện)
    - CreatedAt (Thời gian tạo)
A9. Hệ thống lưu thông tin gán môn học vào cơ sở dữ liệu
A10. Hệ thống hiển thị thông báo "Gán môn học thành công!"
A11. Hệ thống cập nhật lại danh sách môn học của học viên

### Dòng sự kiện B - Hủy gán môn học

B1. Admin chọn học viên từ danh sách
B2. Hệ thống hiển thị danh sách môn học đã được gán cho học viên
B3. Admin chọn môn học cần hủy gán
B4. Admin nhấn nút "Hủy gán"
B5. Hệ thống hiển thị hộp thoại xác nhận với thông tin:
    - Tên học viên
    - Tên môn học
    - Cảnh báo về việc hủy gán môn học
B6. Admin xác nhận hủy gán
B7. Hệ thống kiểm tra ràng buộc dữ liệu:
    - Học viên có lịch sử thi môn học này không
    - Học viên có kết quả thi đã lưu cho môn học này không
B8. Hệ thống xóa bản ghi gán môn học khỏi cơ sở dữ liệu
B9. Hệ thống hiển thị thông báo "Hủy gán môn học thành công!"
B10. Hệ thống cập nhật lại danh sách môn học của học viên

### Dòng sự kiện C - Xem môn học của học viên

C1. Admin chọn học viên từ danh sách
C2. Admin nhấn nút "Xem môn học"
C3. Hệ thống truy vấn tất cả môn học được gán cho học viên đã chọn
C4. Hệ thống hiển thị danh sách môn học của học viên với thông tin:
    - Mã môn học
    - Tên môn học
    - Ngày gán môn học
    - Người thực hiện gán
    - Trạng thái (đã thi/chưa thi)
C5. Admin có thể in hoặc xuất danh sách môn học của học viên

### Dòng sự kiện D - Xem học viên theo môn học

D1. Admin chọn môn học từ danh sách
D2. Admin nhấn nút "Xem học viên"
D3. Hệ thống truy vấn tất cả học viên được gán môn học đã chọn
D4. Hệ thống hiển thị danh sách học viên của môn học với thông tin:
    - Mã học viên
    - Tên đăng nhập
    - Họ tên
    - Ngày gán môn học
    - Trạng thái thi
C5. Admin có thể in hoặc xuất danh sách học viên theo môn học

## Dòng sự kiện phụ

- **Gán môn học trùng lặp**: Nếu học viên đã được gán môn học, hệ thống hiển thị thông báo "Học viên đã được gán môn học này!" và không thực hiện thao tác
- **Không phải học viên**: Nếu người dùng được chọn không có vai trò User/Student, hệ thống hiển thị thông báo "Chỉ có thể gán môn học cho học viên!" và từ chối thao tác
- **Ràng buộc dữ liệu khi hủy gán**: 
  - Nếu học viên có lịch sử thi môn học, hệ thống hiển thị cảnh báo "Học viên đã có lịch sử thi môn học này. Bạn có chắc chắn muốn hủy gán?"
  - Nếu học viên có kết quả thi, hệ thống yêu cầu xác nhận và cảnh báo về việc mất dữ liệu
- **Không có dữ liệu**: Nếu không có học viên hoặc môn học nào, hệ thống hiển thị thông báo tương ứng
- **Không có môn học khả dụng**: Nếu học viên đã được gán tất cả môn học, hệ thống hiển thị "Học viên đã được gán tất cả môn học có sẵn"
- **Lỗi kết nối cơ sở dữ liệu**: Hệ thống hiển thị thông báo lỗi và cho phép thử lại
- **Hủy thao tác**: Admin có thể nhấn nút "Hủy" để hủy thao tác và quay về danh sách

## Điều kiện tiên quyết

- Admin đã đăng nhập vào hệ thống với quyền quản trị
- Hệ thống cơ sở dữ liệu đang hoạt động bình thường
- Có ít nhất một người dùng với vai trò User hoặc Student trong hệ thống
- Có ít nhất một môn học trong hệ thống
- Admin có quyền truy cập chức năng "Gán môn học cho người thi"

## Hậu điều kiện

- **Thành công**: 
  - Thông tin gán môn học được cập nhật chính xác trong bảng UserSubject
  - Học viên có quyền truy cập và tham gia thi môn học được gán
  - Học viên có thể xem môn học trong danh sách thi của mình
  - Danh sách môn học của học viên được làm mới và hiển thị chính xác
  - Thông báo kết quả thao tác được hiển thị cho Admin
- **Thất bại**: 
  - Không có thông tin gán môn học nào được thay đổi trong cơ sở dữ liệu
  - Thông báo lỗi được hiển thị cho Admin với mô tả cụ thể
  - Admin có thể thực hiện lại thao tác hoặc chọn gán môn học khác
  - Dữ liệu hiện tại trong hệ thống không bị ảnh hưởng

## Quy tắc nghiệp vụ

1. **Quan hệ gán môn học**:
   - Một học viên có thể được gán nhiều môn học (1-n)
   - Một môn học có thể được gán cho nhiều học viên (1-n)
   - Quan hệ tổng thể là nhiều-nhiều (n-n) thông qua bảng UserSubject

2. **Ràng buộc vai trò**:
   - Chỉ người dùng có RoleID = "User" hoặc "Student" mới có thể được gán môn học
   - Admin và Teacher không thể được gán môn học để thi
   - Phải kiểm tra vai trò trước khi thực hiện gán

3. **Ràng buộc duy nhất**:
   - Không được gán môn học trùng lặp (cùng UserID và SubjectID)
   - Mỗi bản ghi gán môn học phải có ID duy nhất

4. **Quyền truy cập sau gán môn học**:
   - Học viên chỉ có thể thi các môn học được gán
   - Học viên có thể xem kết quả thi của các môn học được gán
   - Học viên có thể xem bảng xếp hạng của các môn học được gán

5. **Ràng buộc hủy gán**:
   - Cảnh báo khi hủy gán môn học nếu học viên đã có lịch sử thi
   - Yêu cầu xác nhận khi hủy gán nếu có kết quả thi đã lưu
   - Phải kiểm tra tất cả dữ liệu liên quan trước khi hủy gán

6. **Tính nhất quán dữ liệu**:
   - Gán môn học phải đồng bộ với quyền thi trong hệ thống
   - Khi hủy gán, cần cập nhật các dữ liệu liên quan

## Giao diện liên quan

- **frmManageUserSubject**: Form chính gán môn học cho học viên
  - `grv_Users`: DataGridView hiển thị danh sách học viên
  - `grv_Subjects`: DataGridView hiển thị danh sách môn học
  - `grv_UserSubjects`: DataGridView hiển thị môn học đã gán cho học viên
  - `btn_Assign`: Nút gán môn học
  - `btn_Remove`: Nút hủy gán môn học
  - `btn_ViewByUser`: Nút xem môn học theo học viên
  - `btn_ViewBySubject`: Nút xem học viên theo môn học

- **Các lớp nghiệp vụ liên quan**:
  - **BUserSubject**: Xử lý logic gán môn học cho học viên
    - `Insert(int userId, string subjectId, string createdBy)`: Gán môn học mới
    - `Delete(int userId, string subjectId)`: Hủy gán môn học
    - `GetByUser(int userId)`: Lấy môn học của học viên
    - `GetBySubject(string subjectId)`: Lấy học viên theo môn học
    - `GetAll()`: Lấy tất cả thông tin gán môn học
  
  - **BUserAccount**: Lấy danh sách học viên
    - `GetByRole(string roleId)`: Lấy danh sách theo vai trò User/Student
  
  - **BSubject**: Lấy danh sách môn học
    - `GetAll()`: Lấy tất cả môn học

## Luồng dữ liệu

1. **Input**: UserID (học viên), SubjectID (môn học), Admin thực hiện
2. **Processing**: Kiểm tra ràng buộc, tạo bản ghi UserSubject
3. **Output**: Thông báo kết quả, cập nhật danh sách gán môn học
4. **Storage**: Lưu vào bảng UserSubject trong cơ sở dữ liệu
