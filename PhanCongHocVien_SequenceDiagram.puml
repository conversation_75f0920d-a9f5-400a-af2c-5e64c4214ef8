@startuml PhanCongHocVien_SequenceDiagram

!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

title Sơ đồ tuần tự - <PERSON><PERSON>ôn học cho Học viên

actor Admin
participant "frmManageUserSubject" as Form
participant "BUserAccount" as UserBLL
participant "BSubject" as SubjectBLL
participant "BUserSubject" as UserSubjectBLL
participant "DUserAccount" as UserDAL
participant "DSubject" as SubjectDAL
participant "DUserSubject" as UserSubjectDAL
participant "SqlHelper" as DB
database "Database" as Database

== Khởi tạo Form ==
Admin -> Form: Mở form gán môn học cho học viên
activate Form

Form -> Form: frmManageUserSubject_Load()
Form -> Form: LoadUsers()
activate Form
Form -> UserBLL: GetByRole("User")
activate UserBLL
UserBLL -> UserDAL: GetByRole("User")
activate UserDAL
UserDAL -> DB: ExecuteData("UserAccount_GetByRole", "User")
activate DB
DB -> Database: SELECT * FROM UserAccount WHERE RoleID = 'User'
activate Database
Database --> DB: Users DataTable
deactivate Database
DB --> UserDAL: Users DataTable
deactivate DB
UserDAL --> UserBLL: Users DataTable
deactivate UserDAL
UserBLL --> Form: Users DataTable
deactivate UserBLL
Form -> Form: grv_Users.DataSource = Users
deactivate Form

Form -> Form: LoadSubjects()
activate Form
Form -> SubjectBLL: GetAll()
activate SubjectBLL
SubjectBLL -> SubjectDAL: GetAll()
activate SubjectDAL
SubjectDAL -> DB: ExecuteData("Subject_SelectAll")
activate DB
DB -> Database: SELECT * FROM Subject
activate Database
Database --> DB: Subjects DataTable
deactivate Database
DB --> SubjectDAL: Subjects DataTable
deactivate DB
SubjectDAL --> SubjectBLL: Subjects DataTable
deactivate SubjectDAL
SubjectBLL --> Form: Subjects DataTable
deactivate SubjectBLL
Form -> Form: grv_Subjects.DataSource = Subjects
deactivate Form

Form -> Form: LoadUserSubjects()
activate Form
Form -> UserSubjectBLL: GetAll()
activate UserSubjectBLL
UserSubjectBLL -> UserSubjectDAL: GetAll()
activate UserSubjectDAL
UserSubjectDAL -> DB: ExecuteData("UserSubject_SelectAll")
activate DB
DB -> Database: SELECT * FROM UserSubject
activate Database
Database --> DB: UserSubjects DataTable
deactivate Database
DB --> UserSubjectDAL: UserSubjects DataTable
deactivate DB
UserSubjectDAL --> UserSubjectBLL: UserSubjects DataTable
deactivate UserSubjectDAL
UserSubjectBLL --> Form: UserSubjects DataTable
deactivate UserBLL
Form -> Form: grv_UserSubjects.DataSource = UserSubjects
deactivate Form

Form --> Admin: Hiển thị form với dữ liệu đã load
deactivate Form

== Gán môn học cho học viên ==
Admin -> Form: Chọn học viên từ grv_Users
activate Form
Form -> Form: grv_Users_SelectionChanged()
Form -> Form: selectedUserId = grv_Users.SelectedRows[0].Cells["colUserID"].Value
Form -> Form: UpdateButtonStatus()
deactivate Form

Admin -> Form: Chọn môn học từ grv_Subjects
activate Form
Form -> Form: grv_Subjects_SelectionChanged()
Form -> Form: selectedSubjectId = grv_Subjects.SelectedRows[0].Cells["colSubjectID"].Value
Form -> Form: UpdateButtonStatus()
deactivate Form

Admin -> Form: btn_Assign_Click()
activate Form

alt selectedUserId > 0 && selectedSubjectId != ""
    Form -> UserSubjectBLL: Insert(selectedUserId, selectedSubjectId, Session.LogonUser.Username)
    activate UserSubjectBLL
    UserSubjectBLL -> UserSubjectDAL: Insert(userId, subjectId, createdBy)
    activate UserSubjectDAL
    UserSubjectDAL -> DB: ExecuteNonQuery("UserSubject_Insert", parameters)
    activate DB
    DB -> Database: INSERT INTO UserSubject VALUES(userId, subjectId, createdBy, ...)
    activate Database
    
    alt Insert successful
        Database --> DB: Success
        deactivate Database
        DB --> UserSubjectDAL: Success
        deactivate DB
        UserSubjectDAL --> UserSubjectBLL: Success
        deactivate UserSubjectDAL
        UserSubjectBLL --> Form: Success
        deactivate UserSubjectBLL
        
        Form -> Form: XtraMessageBox.Show("Gán môn học thành công!")
        Form -> Form: LoadUserSubjects()
        activate Form
        Form -> UserSubjectBLL: GetAll()
        activate UserSubjectBLL
        UserSubjectBLL -> UserSubjectDAL: GetAll()
        activate UserSubjectDAL
        UserSubjectDAL -> DB: ExecuteData("UserSubject_SelectAll")
        activate DB
        DB -> Database: SELECT * FROM UserSubject
        activate Database
        Database --> DB: Updated UserSubjects DataTable
        deactivate Database
        DB --> UserSubjectDAL: Updated UserSubjects DataTable
        deactivate DB
        UserSubjectDAL --> UserSubjectBLL: Updated UserSubjects DataTable
        deactivate UserSubjectDAL
        UserSubjectBLL --> Form: Updated UserSubjects DataTable
        deactivate UserSubjectBLL
        Form -> Form: grv_UserSubjects.DataSource = Updated DataTable
        deactivate Form
        
        Form --> Admin: Hiển thị thông báo thành công và cập nhật danh sách
        
    else Insert failed (Duplicate key)
        Database --> DB: Error (Duplicate key)
        deactivate Database
        DB --> UserSubjectDAL: Exception
        deactivate DB
        UserSubjectDAL --> UserSubjectBLL: Exception
        deactivate UserSubjectDAL
        UserSubjectBLL --> Form: Exception
        deactivate UserSubjectBLL
        Form -> Form: XtraMessageBox.Show("Học viên đã được gán môn học này!")
        Form --> Admin: Hiển thị thông báo lỗi
    end
    
else Chưa chọn học viên hoặc môn học
    Form -> Form: XtraMessageBox.Show("Vui lòng chọn học viên và môn học!")
    Form --> Admin: Hiển thị thông báo lỗi
end

deactivate Form

== Hủy gán môn học ==
Admin -> Form: Chọn bản ghi từ grv_UserSubjects
Admin -> Form: btn_Remove_Click()
activate Form

Form -> Form: Lấy userId và subjectId từ selected row
Form -> Form: XtraMessageBox.Show("Bạn có chắc chắn muốn xóa?")

alt Admin chọn Yes
    Form -> UserSubjectBLL: Delete(userId, subjectId)
    activate UserSubjectBLL
    UserSubjectBLL -> UserSubjectDAL: Delete(userId, subjectId)
    activate UserSubjectDAL
    UserSubjectDAL -> DB: ExecuteNonQuery("UserSubject_Delete", parameters)
    activate DB
    DB -> Database: DELETE FROM UserSubject WHERE UserID = ? AND SubjectID = ?
    activate Database
    Database --> DB: Success
    deactivate Database
    DB --> UserSubjectDAL: Success
    deactivate DB
    UserSubjectDAL --> UserSubjectBLL: Success
    deactivate UserSubjectDAL
    UserSubjectBLL --> Form: Success
    deactivate UserSubjectBLL
    
    Form -> Form: XtraMessageBox.Show("Xóa bản ghi thành công!")
    Form -> Form: LoadUserSubjects()
    Form --> Admin: Hiển thị thông báo thành công và cập nhật danh sách
    
else Admin chọn No
    Form --> Admin: Hủy thao tác
end

deactivate Form

@enduml
