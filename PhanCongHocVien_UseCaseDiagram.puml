@startuml PhanCongHocVien_UseCaseDiagram

!theme plain
skinparam actorStyle awesome
skinparam usecase {
    BackgroundColor LightGreen
    BorderColor DarkGreen
    FontSize 10
}
skinparam rectangle {
    BackgroundColor LightYellow
    BorderColor Orange
    FontStyle bold
}

title Sơ đồ Use Case - Phân công Học viên

' Actors
actor "Admin\n(<PERSON>uản trị viên)" as admin

rectangle "Hệ thống Gán môn học cho Học viên" {

    ' Authentication Package
    package "Xác thực" {
        usecase "Đăng nhập" as login
        usecase "Đăng xuất" as logout
        usecase "<PERSON><PERSON><PERSON> thực quyền quản trị" as checkadmin
    }

    ' Student Assignment Management Package - Main Focus
    package "Quản lý Gán môn học" {
        usecase "Gán môn học cho học viên" as assignstudent
        usecase "Thêm gán môn học" as addassignment
        usecase "H<PERSON>y gán môn học" as removeassignment
        usecase "Xem môn học của học viên" as viewbyuser
        usecase "Xem học viên theo môn học" as viewbysubject
    }

    ' Selection Package
    package "Lựa chọn Dữ liệu" {
        usecase "Chọn học viên" as selectstudent
        usecase "Chọn môn học" as selectsubject
        usecase "Lấy danh sách học viên" as getstudents
        usecase "Lấy danh sách môn học" as getsubjects
        usecase "Lấy môn học khả dụng" as getavailablesubjects
        usecase "Lấy danh sách gán môn học" as getassignments
    }

    ' Validation Package
    package "Xác thực Dữ liệu" {
        usecase "Kiểm tra gán môn học tồn tại" as checkassignment
        usecase "Kiểm tra ràng buộc dữ liệu" as checkconstraints
        usecase "Xác thực vai trò học viên" as checkstudentrole
    }

    ' Data Processing Package
    package "Xử lý Dữ liệu" {
        usecase "Lưu thông tin gán môn học" as saveassignment
        usecase "Xóa thông tin gán môn học" as deleteassignment
        usecase "Cập nhật danh sách gán môn học" as updatelist
        usecase "Hiển thị thông báo kết quả" as showmessage
    }

}

' Actor-UseCase Relationships

' Authentication
admin --> login
admin --> logout

' Main Student Assignment Management Use Cases
admin --> assignstudent
admin --> addassignment
admin --> removeassignment
admin --> viewbyuser
admin --> viewbysubject

' Selection Use Cases
admin --> selectstudent
admin --> selectsubject
admin --> getstudents
admin --> getsubjects
admin --> getavailablesubjects
admin --> getassignments

' Validation Use Cases
admin --> checkassignment
admin --> checkconstraints
admin --> checkstudentrole

' Data Processing Use Cases
admin --> saveassignment
admin --> deleteassignment
admin --> updatelist
admin --> showmessage

' Include relationships
assignstudent ..> checkadmin : <<include>>
assignstudent ..> getstudents : <<include>>
assignstudent ..> getsubjects : <<include>>
assignstudent ..> getassignments : <<include>>

addassignment ..> selectstudent : <<include>>
addassignment ..> getavailablesubjects : <<include>>
addassignment ..> selectsubject : <<include>>
addassignment ..> checkstudentrole : <<include>>
addassignment ..> checkassignment : <<include>>
addassignment ..> saveassignment : <<include>>
addassignment ..> updatelist : <<include>>
addassignment ..> showmessage : <<include>>

removeassignment ..> selectstudent : <<include>>
removeassignment ..> selectsubject : <<include>>
removeassignment ..> checkconstraints : <<include>>
removeassignment ..> deleteassignment : <<include>>
removeassignment ..> updatelist : <<include>>
removeassignment ..> showmessage : <<include>>

viewbyuser ..> selectstudent : <<include>>
viewbyuser ..> getassignments : <<include>>

viewbysubject ..> selectsubject : <<include>>
viewbysubject ..> getassignments : <<include>>

' Extend relationships
assignstudent ..> addassignment : <<extend>>
assignstudent ..> removeassignment : <<extend>>
assignstudent ..> viewbyuser : <<extend>>
assignstudent ..> viewbysubject : <<extend>>

@enduml
