@startuml DesignLevel_ClassDiagram

!theme plain
skinparam classAttributeIconSize 0
skinparam classFontSize 10
skinparam classAttributeFontSize 8
skinparam classOperationFontSize 8
skinparam linetype ortho

title S<PERSON> đồ lớp mức thiết kế - <PERSON>u<PERSON>n lý <PERSON>, <PERSON><PERSON> công Gi<PERSON>o viên và Học viên

package "Entities" {
    class Subject {
        +SubjectId : string
        +SubjectName : string
        +Description : string
        +CreatedBy : string
        +ModifiedBy : string
        +CreatedAt : DateTime
        +ModifiedAt : DateTime
        __
        +Subject()
        +Subject(subjectId : string, subjectName : string, description : string)
    }

    class UserAccount {
        +UserID : int
        +RoleID : string
        +Username : string
        +Password : string
        +NewPassword : string
        +ConfirmNewPassword : string
        +Fullname : string
        +Email : string
        +PhoneNumber : string
        +Address : string
        +Birthday : DateTime
        +Note : string
        +Image : byte[]
        +CreatedBy : string
        +ModifiedBy : string
        +CreatedAt : DateTime
        +ModifiedAt : DateTime
        __
        +UserAccount()
        +UserAccount(username : string, password : string, fullname : string)
    }

    class TeacherSubject {
        +ID : int
        +UserID : int
        +SubjectID : string
        +SubjectName : string
        +CreatedBy : string
        +ModifiedBy : string
        +CreatedAt : DateTime
        +ModifiedAt : DateTime
        __
        +TeacherSubject()
        +TeacherSubject(userID : int, subjectID : string, createdBy : string)
    }

    class UserSubject {
        +ID : int
        +UserID : int
        +SubjectID : string
        +Username : string
        +Fullname : string
        +SubjectName : string
        +CreatedBy : string
        +CreatedAt : DateTime
        +ModifiedBy : string
        +ModifiedAt : DateTime
        __
        +UserSubject()
        +UserSubject(userID : int, subjectID : string, createdBy : string)
    }
}

package "BusinessLogicLayer" {
    class BSubject <<static>> {
        __
        +AddNewSubject(newSubject : Subject) : void
        +GetAll() : DataTable
        +UpdateSubject(editSubject : Subject) : void
        +DeleteSubject(subjectId : string) : void
        +Search(keyword : string) : DataTable
        +IsSubjectExist(subjectId : string) : bool
    }

    class BUserAccount {
        __
        +IsExistsAccount(user : UserAccount) : bool
        +UpdatePassword(user : UserAccount) : void
        +AddNewUser(newUser : UserAccount) : void
        +GetAll() : DataTable
        +UpdateUser(editUser : UserAccount) : void
        +DeleteUser(userId : int) : void
        +Search(keyword : string, roleFilter : string) : DataTable
        +GetInforUser(username : string) : UserAccount
        +GetPasswordByEmail(email : string) : UserAccount
        +IsEmailExists(email : string) : bool
        +GetByRole(roleId : string) : DataTable
        +GetBySubject(subjectId : string) : DataTable
    }

    class BTeacherSubject {
        __
        +AddTeacherSubject(teacherSubject : TeacherSubject) : void
        +GetByTeacher(userId : int) : DataTable
        +DeleteTeacherSubject(id : int) : void
        +IsTeacherAssigned(userId : int, subjectId : string) : bool
    }

    class BUserSubject {
        __
        +Insert(userId : int, subjectId : string, createdBy : string) : void
        +Delete(userId : int, subjectId : string) : void
        +GetByUser(userId : int) : DataTable
        +GetBySubject(subjectId : string) : DataTable
        +GetAll() : DataTable
    }
}

package "DataAccessLayer" {
    class DSubject {
        __
        +AddNewSubject(newSubject : Subject) : void
        +GetAll() : DataTable
        +UpdateSubject(editSubject : Subject) : void
        +DeleteSubject(subjectId : string) : void
        +Search(keyword : string) : DataTable
        +IsSubjectExist(subjectID : string) : bool
        -ExecuteStoredProcedure(procedureName : string, parameters : SqlParameter[]) : void
        -HandleException(ex : Exception) : void
    }

    class DUserAccount {
        __
        +IsExistsAccount(user : UserAccount) : bool
        +UpdatePassword(user : UserAccount) : void
        +AddNewUser(newUser : UserAccount) : void
        +GetAll() : DataTable
        +UpdateUser(editUser : UserAccount) : void
        +DeleteUser(userId : int) : void
        +Search(keyword : string, roleFilter : string) : DataTable
        +GetInforUser(username : string) : UserAccount
        +GetPasswordByEmail(email : string) : UserAccount
        +IsEmailExists(email : string) : bool
        +GetByRole(roleId : string) : DataTable
        +GetBySubject(subjectId : string) : DataTable
        -CreateUserFromDataRow(row : DataRow) : UserAccount
        -ValidateUserData(user : UserAccount) : bool
        -HandleDatabaseException(ex : Exception) : void
    }

    class DTeacherSubject {
        __
        +AddTeacherSubject(teacherSubject : TeacherSubject) : void
        +GetByTeacher(userId : int) : DataTable
        +DeleteTeacherSubject(id : int) : void
        +IsTeacherAssigned(userId : int, subjectId : string) : bool
        -CreateOutputParameter(paramName : string, sqlType : SqlDbType) : SqlParameter
        -ValidateTeacherSubjectData(teacherSubject : TeacherSubject) : bool
    }

    class DUserSubject {
        __
        +Insert(userId : int, subjectId : string, createdBy : string) : void
        +Delete(userId : int, subjectId : string) : void
        +GetByUser(userId : int) : DataTable
        +GetBySubject(subjectId : string) : DataTable
        +GetAll() : DataTable
        -ValidateUserSubjectData(userId : int, subjectId : string) : bool
        -CheckUserSubjectExists(userId : int, subjectId : string) : bool
    }
}

package "PresentationLayer" {
    class frm_ManageSubject {
        -isAddNew : bool
        -rowIndex : int
        -strMessageInputSearch : string
        __
        +frm_ManageSubject()
        +frmManageSubject_Load(sender : object, e : EventArgs) : void
        +btn_Add_Click(sender : object, e : EventArgs) : void
        +btn_Edit_Click(sender : object, e : EventArgs) : void
        +btn_Delete_Click(sender : object, e : EventArgs) : void
        +btn_Save_Click(sender : object, e : EventArgs) : void
        +btn_Cancel_Click(sender : object, e : EventArgs) : void
        +btn_Search_Click(sender : object, e : EventArgs) : void
        -LoadData() : void
        -GetSubjectInfor() : Subject
        -IsValidInfor(subject : Subject) : bool
        -IsDuplicateSubject(subjectId : string, subjectName : string) : bool
        -AddNewSubject() : void
        -UpdateSubject() : void
        -ShowDetailData(rowIndex : int) : void
        -ShowHideButton(isShow : bool) : void
        -SetEnableControl(isEnable : bool) : void
    }

    class frmTeacherSubject {
        __
        +frmTeacherSubject()
        +frmTeacherSubject_Load(sender : object, e : EventArgs) : void
        +btn_Add_Click(sender : object, e : EventArgs) : void
        +btn_Delete_Click(sender : object, e : EventArgs) : void
        +cbb_Teacher_SelectedIndexChanged(sender : object, e : EventArgs) : void
        -LoadTeachers() : void
        -LoadSubjects() : void
        -LoadTeacherSubjects() : void
        -ValidateSelection() : bool
        -GetSelectedTeacherId() : int
        -GetSelectedSubjectId() : string
    }

    class frmManageUserSubject {
        -dtUsers : DataTable
        -dtSubjects : DataTable
        -dtUserSubjects : DataTable
        -selectedUserId : int
        -selectedSubjectId : string
        __
        +frmManageUserSubject()
        +frmManageUserSubject_Load(sender : object, e : EventArgs) : void
        +btn_Assign_Click(sender : object, e : EventArgs) : void
        +btn_Remove_Click(sender : object, e : EventArgs) : void
        +btn_Refresh_Click(sender : object, e : EventArgs) : void
        +grv_Users_SelectionChanged(sender : object, e : EventArgs) : void
        +grv_Subjects_SelectionChanged(sender : object, e : EventArgs) : void
        -LoadUsers() : void
        -LoadSubjects() : void
        -LoadUserSubjects() : void
        -UpdateButtonStatus() : void
        -ValidateAssignment() : bool
    }
}

package "Utilities" {
    class SqlHelper <<static>> {
        __
        +ExecuteNonQuery(connectionString : string, commandText : string, parameters : object[]) : int
        +ExecuteNonQuery(connectionString : string, commandType : CommandType, commandText : string, parameters : SqlParameter[]) : int
        +ExecuteData(connectionString : string, commandType : CommandType, commandText : string, parameters : SqlParameter[]) : DataTable
        +ExecuteScalar(connectionString : string, commandText : string, parameters : object[]) : object
        -CreateConnection(connectionString : string) : SqlConnection
        -CreateCommand(commandText : string, connection : SqlConnection, commandType : CommandType) : SqlCommand
        -AddParameters(command : SqlCommand, parameters : SqlParameter[]) : void
    }

    class Session <<static>> {
        +LogonUser : UserAccount
        __
        +SetLogonUser(user : UserAccount) : void
        +GetLogonUser() : UserAccount
        +ClearSession() : void
        +IsUserLoggedIn() : bool
    }
}

UserAccount ||--o{ TeacherSubject : "1-n"
Subject ||--o{ TeacherSubject : "1-n"
UserAccount ||--o{ UserSubject : "1-n"
Subject ||--o{ UserSubject : "1-n"

BSubject ..> DSubject : uses
BUserAccount ..> DUserAccount : uses
BTeacherSubject ..> DTeacherSubject : uses
BUserSubject ..> DUserSubject : uses

DSubject ..> Subject : uses
DUserAccount ..> UserAccount : uses
DTeacherSubject ..> TeacherSubject : uses
DUserSubject ..> UserSubject : uses

frm_ManageSubject ..> BSubject : uses
frm_ManageSubject ..> Subject : uses
frmTeacherSubject ..> BTeacherSubject : uses
frmTeacherSubject ..> BUserAccount : uses
frmTeacherSubject ..> BSubject : uses
frmTeacherSubject ..> TeacherSubject : uses
frmManageUserSubject ..> BUserSubject : uses
frmManageUserSubject ..> BUserAccount : uses
frmManageUserSubject ..> BSubject : uses

DSubject ..> SqlHelper : uses
DUserAccount ..> SqlHelper : uses
DTeacherSubject ..> SqlHelper : uses
DUserSubject ..> SqlHelper : uses
frm_ManageSubject ..> Session : uses
frmTeacherSubject ..> Session : uses
frmManageUserSubject ..> Session : uses

@enduml
