@startuml SubjectManagement_ClassDiagram

!theme plain
skinparam classAttributeIconSize 0
skinparam classFontSize 10
skinparam classAttributeFontSize 9
skinparam classOperationFontSize 9

title S<PERSON> đồ lớp - <PERSON><PERSON><PERSON><PERSON> v<PERSON> l<PERSON>, <PERSON><PERSON> công Gi<PERSON>o viên và Học viên

class Subject {
    +SubjectId: string
    +SubjectName: string
    +Description: string
    +CreatedBy: string
    +ModifiedBy: string
    +CreatedAt: DateTime
    +ModifiedAt: DateTime
    --
    +AddNewSubject(newSubject: Subject): void
    +GetAll(): DataTable
    +UpdateSubject(editSubject: Subject): void
    +DeleteSubject(subjectId: string): void
    +Search(keyword: string): DataTable
    +IsSubjectExist(subjectId: string): bool
}

class Teacher {
    +UserID: int
    +Username: string
    +Fullname: string
    +Email: string
    +PhoneNumber: string
    +Address: string
    +Birthday: DateTime
    +CreatedBy: string
    +ModifiedBy: string
    +CreatedAt: DateTime
    +ModifiedAt: DateTime
    --
    +AddNewUser(newUser: UserAccount): void
    +GetAll(): DataTable
    +UpdateUser(editUser: UserAccount): void
    +DeleteUser(userId: int): void
    +Search(keyword: string, roleFilter: string): DataTable
    +GetInforUser(username: string): UserAccount
    +GetByRole(roleId: string): DataTable
}

class Student {
    +UserID: int
    +Username: string
    +Fullname: string
    +Email: string
    +PhoneNumber: string
    +Address: string
    +Birthday: DateTime
    +CreatedBy: string
    +ModifiedBy: string
    +CreatedAt: DateTime
    +ModifiedAt: DateTime
    --
    +AddNewUser(newUser: UserAccount): void
    +GetAll(): DataTable
    +UpdateUser(editUser: UserAccount): void
    +DeleteUser(userId: int): void
    +Search(keyword: string, roleFilter: string): DataTable
    +GetInforUser(username: string): UserAccount
    +GetByRole(roleId: string): DataTable
}

class TeacherSubject {
    +ID: int
    +UserID: int
    +SubjectID: string
    +SubjectName: string
    +CreatedBy: string
    +ModifiedBy: string
    +CreatedAt: DateTime
    +ModifiedAt: DateTime
    --
    +AddTeacherSubject(teacherSubject: TeacherSubject): void
    +GetByTeacher(userId: int): DataTable
    +DeleteTeacherSubject(id: int): void
    +IsTeacherAssigned(userId: int, subjectId: string): bool
}

class UserSubject {
    +ID: int
    +UserID: int
    +SubjectID: string
    +Username: string
    +Fullname: string
    +SubjectName: string
    +CreatedBy: string
    +CreatedAt: DateTime
    +ModifiedBy: string
    +ModifiedAt: DateTime
    --
    +Insert(userId: int, subjectId: string, createdBy: string): void
    +Delete(userId: int, subjectId: string): void
    +GetByUser(userId: int): DataTable
    +GetBySubject(subjectId: string): DataTable
    +GetAll(): DataTable
}

' Relationships
Teacher ||--o{ TeacherSubject : "1-n"
Subject ||--o{ TeacherSubject : "1-n"
Student ||--o{ UserSubject : "1-n"
Subject ||--o{ UserSubject : "1-n"

@enduml
