# Đặc tả Use Case - Chức năng Báo cáo

## Thông tin chung

| Mục | Mô tả |
|-----|-------|
| **Tên use case** | Xem báo cáo hệ thống |
| **Tóm tắt** | Use case mô tả quá trình người dùng thực hiện xem và xuất các báo cáo thống kê trong hệ thống thi trắc nghiệm |
| **Tác nhân** | <PERSON><PERSON> (Quản trị viên), Teacher (Giáo viên) |
| **Use case liên quan** | - L<PERSON>y dữ liệu báo cáo (include)<br/>- <PERSON><PERSON><PERSON> thị báo cáo (include)<br/>- Xuất báo cáo PDF (extend)<br/>- <PERSON><PERSON><PERSON> thực quyền truy cập (include) |

## Dòng sự kiện chính

1. Người dùng đăng nhập và<PERSON> hệ thống
2. <PERSON><PERSON> thống xác thực quyền truy cập báo cáo của người dùng
3. <PERSON><PERSON> thống hiển thị menu báo cáo phù hợp với quyền của người dùng
4. Người dùng chọn loại báo cáo muốn xem:
   - **Báo cáo theo môn học**: Chuyển đến dòng sự kiện A
   - **Báo cáo theo thời gian**: Chuyển đến dòng sự kiện B (chỉ Admin)
   - **Báo cáo kỳ thi**: Chuyển đến dòng sự kiện C (chỉ Admin)

### Dòng sự kiện A - Báo cáo theo môn học

A1. Người dùng chọn "Báo cáo theo môn học"
A2. Hệ thống hiển thị danh sách môn học mà người dùng có quyền xem:
    - Admin: Tất cả môn học
    - Teacher: Chỉ môn học được phân công
A3. Người dùng chọn môn học từ ComboBox
A4. Người dùng nhấn nút "Xem báo cáo"
A5. Hệ thống lấy dữ liệu thống kê kết quả thi theo môn học đã chọn
A6. Hệ thống kiểm tra dữ liệu có tồn tại không
A7. Hệ thống hiển thị báo cáo với các thông tin:
    - Tên môn học
    - Danh sách học viên đã thi
    - Điểm số từng học viên
    - Thống kê tổng quan
A8. Hệ thống cung cấp tùy chọn xuất báo cáo ra PDF

### Dòng sự kiện B - Báo cáo theo thời gian (Admin only)

B1. Admin chọn "Báo cáo theo thời gian"
B2. Hệ thống hiển thị form chọn khoảng thời gian với:
    - DateTimePicker từ ngày (mặc định: 30 ngày trước)
    - DateTimePicker đến ngày (mặc định: ngày hiện tại)
B3. Admin chọn khoảng thời gian cần báo cáo
B4. Admin nhấn nút "Xem báo cáo"
B5. Hệ thống lấy dữ liệu tất cả kết quả thi trong khoảng thời gian đã chọn
B6. Hệ thống kiểm tra dữ liệu có tồn tại không
B7. Hệ thống hiển thị báo cáo với các thông tin:
    - Khoảng thời gian báo cáo
    - Danh sách tất cả bài thi trong khoảng thời gian
    - Thông tin học viên, môn học, điểm số
    - Thống kê tổng quan theo thời gian
B8. Hệ thống cung cấp tùy chọn xuất báo cáo ra PDF

### Dòng sự kiện C - Báo cáo kỳ thi (Admin only)

C1. Admin chọn "Báo cáo kỳ thi"
C2. Hệ thống hiển thị danh sách tất cả kỳ thi trong ComboBox
C3. Admin chọn kỳ thi cần báo cáo
C4. Admin nhấn nút "Xem báo cáo" hoặc "In báo cáo"
C5. Hệ thống lấy thông tin chi tiết kỳ thi và danh sách người tham gia
C6. Hệ thống kiểm tra dữ liệu có tồn tại không
C7. Hệ thống hiển thị báo cáo với các thông tin:
    - Thông tin kỳ thi (tên, thời gian, trạng thái)
    - Danh sách người dùng đăng ký tham gia
    - Trạng thái tham gia của từng người dùng
    - Thống kê tổng quan kỳ thi
C8. Hệ thống cung cấp tùy chọn xuất báo cáo ra PDF

### Dòng sự kiện D - Xuất báo cáo PDF

D1. Người dùng nhấn nút "Xuất PDF" từ bất kỳ báo cáo nào
D2. Hệ thống hiển thị hộp thoại chọn vị trí lưu file
D3. Người dùng chọn thư mục và tên file
D4. Người dùng nhấn "Lưu"
D5. Hệ thống tạo file PDF từ dữ liệu báo cáo hiện tại
D6. Hệ thống lưu file PDF vào vị trí đã chọn
D7. Hệ thống hiển thị thông báo "Xuất báo cáo thành công!"

## Dòng sự kiện phụ

- **Không có dữ liệu**: Nếu không có dữ liệu để hiển thị báo cáo, hệ thống hiển thị thông báo "Không có dữ liệu để hiển thị báo cáo"
- **Lỗi quyền truy cập**: Nếu người dùng không có quyền xem báo cáo được chọn, hệ thống hiển thị thông báo lỗi và từ chối truy cập
- **Lỗi kết nối cơ sở dữ liệu**: Nếu không thể kết nối cơ sở dữ liệu, hệ thống hiển thị thông báo lỗi
- **Lỗi xuất PDF**: Nếu không thể tạo hoặc lưu file PDF, hệ thống hiển thị thông báo lỗi và cho phép thử lại
- **Hủy thao tác**: Người dùng có thể hủy thao tác bất kỳ lúc nào và quay về menu báo cáo

## Điều kiện tiên quyết

- Người dùng đã đăng nhập vào hệ thống với quyền phù hợp:
  - Admin: Có quyền xem tất cả báo cáo
  - Teacher: Chỉ có quyền xem báo cáo theo môn học được phân công
- Hệ thống cơ sở dữ liệu đang hoạt động bình thường
- Có dữ liệu lịch sử thi trong hệ thống để tạo báo cáo

## Hậu điều kiện

- **Thành công**: 
  - Báo cáo được hiển thị chính xác với dữ liệu từ cơ sở dữ liệu
  - File PDF được tạo và lưu thành công (nếu có xuất PDF)
  - Thông báo kết quả thao tác được hiển thị cho người dùng
  - Dữ liệu báo cáo phản ánh đúng tình trạng hiện tại của hệ thống
- **Thất bại**: 
  - Không có báo cáo nào được hiển thị hoặc file PDF không được tạo
  - Thông báo lỗi được hiển thị cho người dùng
  - Người dùng có thể thực hiện lại thao tác hoặc chọn báo cáo khác
  - Dữ liệu trong cơ sở dữ liệu không bị ảnh hưởng

## Quy tắc nghiệp vụ

1. **Phân quyền báo cáo**:
   - Admin: Có quyền xem tất cả loại báo cáo
   - Teacher: Chỉ được xem báo cáo theo môn học được phân công
   - Student: Không có quyền truy cập chức năng báo cáo

2. **Dữ liệu báo cáo**:
   - Chỉ hiển thị dữ liệu của học viên (loại trừ Admin và Teacher)
   - Báo cáo theo thời gian chỉ tính các bài thi đã hoàn thành
   - Dữ liệu phải được cập nhật real-time

3. **Xuất PDF**:
   - File PDF phải chứa đầy đủ thông tin như trên màn hình
   - Bao gồm thông tin người tạo báo cáo và thời gian tạo
   - Định dạng file chuẩn, dễ đọc và in ấn

4. **Bảo mật**:
   - Không được phép truy cập dữ liệu ngoài phạm vi quyền hạn
   - Log lại các thao tác xem và xuất báo cáo

## Giao diện liên quan

- **NewFrmReportBySubject**: Form báo cáo theo môn học
  - `cbb_MonThi`: ComboBox chọn môn học
  - `btn_XemBaoCao`: Nút xem báo cáo

- **NewFrmReportByTime**: Form báo cáo theo thời gian
  - `dpk_StartDate`: DateTimePicker chọn ngày bắt đầu
  - `dpk_EndDate`: DateTimePicker chọn ngày kết thúc
  - `btn_XemBaoCao`: Nút xem báo cáo
  - `documentViewer1`: Control hiển thị báo cáo

- **NewFrmReportExamSession**: Form báo cáo kỳ thi
  - `cbb_ExamSession`: ComboBox chọn kỳ thi
  - `btn_ViewReport`: Nút xem báo cáo
  - `btn_Print`: Nút in báo cáo
  - `btn_ExportPdf`: Nút xuất PDF

- **ReportHelper**: Lớp hỗ trợ xử lý báo cáo
  - `ShowSubjectReport()`: Hiển thị báo cáo môn học
  - `ShowTimeReport()`: Hiển thị báo cáo thời gian
  - `ShowExamSessionReport()`: Hiển thị báo cáo kỳ thi
  - `ExportToPdf()`: Xuất báo cáo ra PDF
