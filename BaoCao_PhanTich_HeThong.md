# BÁO CÁO PHÂN TÍCH HỆ THỐNG THI TRẮC NGHIỆM
## Dà<PERSON> cho Đội ngũ Thiết kế Sơ đồ

---

## 1. TỔNG QUAN HỆ THỐNG

### 1.1 <PERSON>ô tả chung
Hệ thống Thi Trắc nghiệm là ứng dụng desktop được phát triển bằng C# .NET Framework, sử dụng kiến trúc 3 lớp (Presentation Layer, Business Logic Layer, Data Access Layer) với cơ sở dữ liệu SQL Server.

### 1.2 Kiến trúc hệ thống
```
┌─────────────────────────────────────┐
│        Presentation Layer           │
│  (Forms: frm_*, NewFrm_*)          │
├─────────────────────────────────────┤
│      Business Logic Layer          │
│        (Classes: B*)               │
├─────────────────────────────────────┤
│       Data Access Layer            │
│        (Classes: D*)               │
├─────────────────────────────────────┤
│         Database Layer             │
│    (SQL Server + SqlHelper)        │
└─────────────────────────────────────┘
```

### 1.3 Các vai trò người dùng
- **Admin**: Quản trị viên hệ thống
- **Teacher**: <PERSON><PERSON><PERSON><PERSON> viên
- **Student**: <PERSON><PERSON> viên/Học viên

---

## 2. C<PERSON><PERSON> CHỨC NĂNG CHÍNH CẦN VẼ SƠ ĐỒ

### 2.1 Quản lý Môn học
**Form chính**: `frm_ManageSubject.cs`

**Các lớp liên quan**:
- **Entity**: `Subject`
- **Business Logic**: `BSubject`
- **Data Access**: `DSubject`

**Luồng xử lý chính**:

#### 2.1.1 Khởi tạo
```
frmManageSubject_Load() → LoadData() → BSubject.GetAll() → DSubject.GetAll() → SqlHelper.ExecuteData("Subject_SelectAll")
```

#### 2.1.2 Thêm mới
```
btn_Add_Click() → btn_Save_Click() → AddNewSubject() → GetSubjectInfor() → IsValidInfor() → IsDuplicateSubject() → BSubject.AddNewSubject() → DSubject.AddNewSubject() → SqlHelper.ExecuteNonQuery("Subject_Insert")
```

#### 2.1.3 Cập nhật
```
btn_Edit_Click() → btn_Save_Click() → UpdateSubject() → BSubject.UpdateSubject() → DSubject.UpdateSubject() → SqlHelper.ExecuteNonQuery("Subject_Update")
```

#### 2.1.4 Xóa
```
btn_Delete_Click() → BSubject.DeleteSubject() → DSubject.DeleteSubject() → SqlHelper.ExecuteNonQuery("Subject_Delete")
```

#### 2.1.5 Tìm kiếm
```
btn_Search_Click() → BSubject.Search() → DSubject.Search() → SqlHelper.ExecuteData("Subject_Search")
```

### 2.2 Phân công Giáo viên
**Form chính**: `frmTeacherSubject.cs`

**Các lớp liên quan**:
- **Entity**: `TeacherSubject`
- **Business Logic**: `BTeacherSubject`, `BUserAccount`, `BSubject`
- **Data Access**: `DTeacherSubject`, `DUserAccount`, `DSubject`

**Luồng xử lý chính**:

#### 2.2.1 Khởi tạo
```
LoadTeachers() → BUserAccount.GetByRole("Teacher") → DUserAccount.GetByRole()
LoadSubjects() → BSubject.GetAll() → DSubject.GetAll()
LoadTeacherSubjects() → BTeacherSubject.GetByTeacher() → DTeacherSubject.GetByTeacher()
```

#### 2.2.2 Thêm phân công
```
btn_Add_Click() → BTeacherSubject.IsTeacherAssigned() → BTeacherSubject.AddTeacherSubject() → DTeacherSubject.AddTeacherSubject() → SqlHelper.ExecuteNonQuery("TeacherSubject_Insert")
```

#### 2.2.3 Xóa phân công
```
btn_Delete_Click() → BTeacherSubject.DeleteTeacherSubject() → DTeacherSubject.DeleteTeacherSubject() → SqlHelper.ExecuteNonQuery("TeacherSubject_Delete")
```

### 2.3 Phân công Học viên
**Form chính**: `frmManageUserSubject.cs`

**Các lớp liên quan**:
- **Business Logic**: `BUserSubject`, `BUserAccount`, `BSubject`
- **Data Access**: `DUserSubject`, `DUserAccount`, `DSubject`

**Luồng xử lý chính**:

#### 2.3.1 Khởi tạo
```
LoadUsers() → BUserAccount.GetByRole("User") → DUserAccount.GetByRole()
LoadSubjects() → BSubject.GetAll() → DSubject.GetAll()
LoadUserSubjects() → BUserSubject.GetAll() → DUserSubject.GetAll()
```

#### 2.3.2 Gán môn học
```
btn_Assign_Click() → BUserSubject.Insert() → DUserSubject.Insert() → SqlHelper.ExecuteNonQuery("UserSubject_Insert")
```

#### 2.3.3 Hủy gán
```
btn_Remove_Click() → BUserSubject.Delete() → DUserSubject.Delete() → SqlHelper.ExecuteNonQuery("UserSubject_Delete")
```

### 2.4 Báo cáo
**Các form chính**: 
- `NewFrmReportBySubject.cs`
- `NewFrmReportByTime.cs`
- `NewFrmReportExamSession.cs`

**Các lớp liên quan**:
- **Business Logic**: `BTestHistory`, `BSubject`, `BExamSession`, `BUserExamSession`
- **Data Access**: `DTestHistory`, `DSubject`, `DExamSession`, `DUserExamSession`
- **Helper**: `ReportHelper`

**Luồng xử lý chính**:

#### 2.4.1 Báo cáo theo môn học
```
LoadData() → BSubject.GetAll() → Load ComboBox môn học
btn_XemBaoCao_Click() → GetReportData() → BTestHistory.GetReportBySubject() → DTestHistory.GetReportBySubject() → SqlHelper.ExecuteData("TestHistory_GetReportBySubject")
ReportHelper.ShowSubjectReport() → Hiển thị báo cáo
```

#### 2.4.2 Báo cáo theo thời gian
```
btn_XemBaoCao_Click() → BTestHistory.GetReportByTime() → DTestHistory.GetReportByTime() → SqlHelper.ExecuteData("TestHistory_GetReportByTime")
ReportHelper.ShowTimeReport() → Hiển thị báo cáo
```

#### 2.4.3 Báo cáo kỳ thi
```
LoadExamSessions() → BExamSession.GetAll() → Load ComboBox kỳ thi
btn_ViewReport_Click() → BExamSession.GetById() + BUserExamSession.GetBySession() → Hiển thị thông tin kỳ thi
btn_ExportPdf_Click() → ReportHelper.ExportToPdf() → Xuất file PDF
```

---

## 3. CẤU TRÚC DỮ LIỆU CHÍNH

### 3.1 Entities (Thực thể)

#### Subject.cs
```csharp
public class Subject
{
    public string SubjectId { get; set; }
    public string SubjectName { get; set; }
    public string Description { get; set; }
    public string CreatedBy { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime ModifiedAt { get; set; }
}
```

#### TeacherSubject.cs
```csharp
public class TeacherSubject
{
    public int ID { get; set; }
    public int UserID { get; set; }
    public string SubjectID { get; set; }
    public string SubjectName { get; set; }
    public string CreatedBy { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime ModifiedAt { get; set; }
}
```

#### UserAccount.cs
```csharp
public class UserAccount
{
    public int UserID { get; set; }
    public string RoleID { get; set; }
    public string Username { get; set; }
    public string Password { get; set; }
    public string Fullname { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public string Address { get; set; }
    public DateTime Birthday { get; set; }
    public string Note { get; set; }
    public byte[] Image { get; set; }
    public string CreatedBy { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime ModifiedAt { get; set; }
}
```

### 3.2 Business Logic Layer

#### BSubject.cs
```csharp
public static class BSubject
{
    public static void AddNewSubject(Subject newSubject)
    public static DataTable GetAll()
    public static void UpdateSubject(Subject editSubject)
    public static void DeleteSubject(string subjectId)
    public static DataTable Search(string keyword)
    public static bool IsSubjectExist(string subjectId)
}
```

#### BTeacherSubject.cs
```csharp
public class BTeacherSubject
{
    public static void AddTeacherSubject(TeacherSubject teacherSubject)
    public static DataTable GetByTeacher(int userId)
    public static void DeleteTeacherSubject(int id)
    public static bool IsTeacherAssigned(int userId, string subjectId)
}
```

#### BUserSubject.cs
```csharp
public class BUserSubject
{
    public static void Insert(int userId, string subjectId, string createdBy)
    public static void Delete(int userId, string subjectId)
    public static DataTable GetByUser(int userId)
    public static DataTable GetBySubject(string subjectId)
    public static DataTable GetAll()
}
```

### 3.3 Data Access Layer

#### DSubject.cs
```csharp
public class DSubject
{
    public static void AddNewSubject(Subject newSubject)
    // SqlHelper.ExecuteNonQuery("Subject_Insert", parameters)
    
    public static DataTable GetAll()
    // SqlHelper.ExecuteData("Subject_SelectAll")
    
    public static void UpdateSubject(Subject editSubject)
    // SqlHelper.ExecuteNonQuery("Subject_Update", parameters)
    
    public static void DeleteSubject(string subjectId)
    // SqlHelper.ExecuteNonQuery("Subject_Delete", subjectId)
    
    public static DataTable Search(string keyword)
    // SqlHelper.ExecuteData("Subject_Search", keyword)
}
```

---

## 4. STORED PROCEDURES SỬ DỤNG

### 4.1 Subject Management
- `Subject_Insert`: Thêm môn học mới
- `Subject_SelectAll`: Lấy tất cả môn học
- `Subject_Update`: Cập nhật thông tin môn học
- `Subject_Delete`: Xóa môn học
- `Subject_Search`: Tìm kiếm môn học

### 4.2 Teacher Assignment
- `TeacherSubject_Insert`: Thêm phân công giáo viên
- `TeacherSubject_GetByTeacher`: Lấy môn học của giáo viên
- `TeacherSubject_Delete`: Xóa phân công
- `TeacherSubject_CheckExists`: Kiểm tra phân công tồn tại

### 4.3 Student Assignment
- `UserSubject_Insert`: Gán môn học cho học viên
- `UserSubject_Delete`: Hủy gán môn học
- `UserSubject_SelectAll`: Lấy tất cả gán môn học
- `UserSubject_GetByUser`: Lấy môn học của học viên

### 4.4 Reports
- `TestHistory_GetReportBySubject`: Báo cáo theo môn học
- `TestHistory_GetReportByTime`: Báo cáo theo thời gian
- `UserExamSession_GetBySession`: Lấy người tham gia kỳ thi

---

## 5. LUỒNG DỮ LIỆU CHÍNH

### 5.1 Pattern chung cho tất cả chức năng
```
User Action → Form Event Handler → Business Logic → Data Access → SqlHelper → Database
                ↓
User Interface ← Form Update ← Business Logic ← Data Access ← SqlHelper ← Database
```

### 5.2 Xử lý lỗi
- Try-catch ở mọi lớp
- Hiển thị thông báo lỗi bằng `XtraMessageBox.Show()`
- Throw exception từ DAL lên BLL và Form

### 5.3 Session Management
- Sử dụng `Session.LogonUser` để lưu thông tin người dùng đăng nhập
- Ghi log `CreatedBy`, `ModifiedBy` cho mọi thao tác

---

## 6. YÊU CẦU VẼ SƠ ĐỒ

### 6.1 Sơ đồ Use Case
**Cần vẽ cho**:
- Toàn hệ thống (tổng quan)
- Từng chức năng riêng biệt (Quản lý môn học, Phân công giáo viên, Phân công học viên, Báo cáo)

**Actors**: Admin, Teacher, Student

**Quan hệ**: Include, Extend theo đúng logic nghiệp vụ

### 6.2 Sơ đồ Lớp (Class Diagram)
**Cần vẽ cho**:
- Quản lý môn học: Subject, BSubject, DSubject
- Phân công giáo viên: TeacherSubject, BTeacherSubject, DTeacherSubject, UserAccount
- Phân công học viên: UserSubject, BUserSubject, DUserSubject, UserAccount
- Báo cáo: TestHistory, ExamSession, UserExamSession và các lớp BLL/DAL tương ứng

**Quan hệ**: 1-1, 1-n, n-n theo đúng thiết kế database

### 6.3 Sơ đồ Tuần tự (Sequence Diagram)
**Cần vẽ cho từng chức năng**:
1. **Quản lý môn học**: Thêm/Sửa/Xóa/Tìm kiếm môn học
2. **Phân công giáo viên**: Thêm/Xóa phân công
3. **Phân công học viên**: Gán/Hủy gán môn học
4. **Báo cáo**: Xem báo cáo theo môn học/thời gian/kỳ thi

**Participants**: Actor, Form, Entity, BLL, DAL, SqlHelper, Database

**Lưu ý**: 
- Không vẽ HTTP status codes (200, OK, etc.)
- Tập trung vào bản chất luồng xử lý
- Thể hiện đúng tên class và method từ mã nguồn

---

## 7. CÔNG CỤ VÀ CHUẨN VẼ

### 7.1 Công cụ đề xuất
- **PlantUML**: Cho sơ đồ Use Case và Sequence
- **Draw.io/Lucidchart**: Cho sơ đồ Class
- **Visual Paradigm**: Cho tất cả loại sơ đồ

### 7.2 Chuẩn UML
- Sử dụng ký hiệu UML chuẩn
- Màu sắc nhất quán cho từng loại thành phần
- Font chữ rõ ràng, dễ đọc

### 7.3 Naming Convention
- Giữ nguyên tên class, method từ mã nguồn
- Sử dụng tiếng Việt cho tên use case
- Ghi chú bằng tiếng Việt khi cần thiết

---

## 8. CHECKLIST HOÀN THÀNH

### 8.1 Sơ đồ Use Case
- [ ] Sơ đồ tổng quan hệ thống
- [ ] Sơ đồ chi tiết Quản lý môn học
- [ ] Sơ đồ chi tiết Phân công giáo viên
- [ ] Sơ đồ chi tiết Phân công học viên
- [ ] Sơ đồ chi tiết Báo cáo

### 8.2 Sơ đồ Lớp
- [ ] Sơ đồ lớp Quản lý môn học
- [ ] Sơ đồ lớp Phân công giáo viên
- [ ] Sơ đồ lớp Phân công học viên
- [ ] Sơ đồ lớp Báo cáo

### 8.3 Sơ đồ Tuần tự
- [ ] Sequence Diagram Quản lý môn học
- [ ] Sequence Diagram Phân công giáo viên
- [ ] Sequence Diagram Phân công học viên
- [ ] Sequence Diagram Báo cáo

---

**Lưu ý quan trọng**: Tất cả sơ đồ phải bám sát mã nguồn thực tế, không được thêm bớt hoặc suy diễn các thành phần không có trong code. Tập trung vào bản chất của luồng xử lý và tương tác giữa các lớp.
