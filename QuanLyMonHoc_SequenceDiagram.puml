@startuml QuanLyMonHoc_SequenceDiagram

!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

title Sơ đồ tuần tự - Quản lý <PERSON> họ<PERSON> (<PERSON>h<PERSON><PERSON> môn họ<PERSON> mới)

actor Admin
participant "frm_ManageSubject" as Form
participant "Subject" as Entity
participant "BSubject" as BLL
participant "DSubject" as DAL
participant "SqlHelper" as DB
database "Database" as Database

== Khởi tạo Form ==
Admin -> Form: Mở form quản lý môn học
activate Form
Form -> BLL: GetAll()
activate BLL
BLL -> DAL: GetAll()
activate DAL
DAL -> DB: ExecuteData("Subject_SelectAll")
activate DB
DB -> Database: SELECT * FROM Subject
activate Database
Database --> DB: DataTable
deactivate Database
DB --> DAL: DataTable
deactivate DB
DAL --> BLL: DataTable
deactivate DAL
BLL --> Form: DataTable
deactivate BLL
Form -> Form: LoadData()
Form -> Form: grv_DataUser.DataSource = DataTable
Form --> Admin: <PERSON><PERSON><PERSON> thị danh sách môn học
deactivate Form

== Thê<PERSON> môn học mới ==
Admin -> Form: btn_Add_Click()
activate Form
Form -> Form: isAddNew = true
Form -> Form: ShowHideButton(true)
Form -> Form: SetEnableControl(true)
Form -> Form: Clear textboxes
Form --> Admin: Hiển thị form nhập liệu
deactivate Form

Admin -> Form: Nhập thông tin môn học
Admin -> Form: btn_Save_Click()
activate Form
Form -> Form: AddNewSubject()
activate Form

Form -> Form: GetSubjectInfor()
activate Form
create Entity
Form -> Entity: new Subject()
Form -> Entity: SubjectId = txt_SubjectId.Text
Form -> Entity: SubjectName = txt_SubjectName.Text
Form -> Entity: Description = txt_SubjectDesb.Text
Form -> Entity: CreatedBy = Session.LogonUser.Username
Form -> Entity: CreatedAt = DateTime.Now
Entity --> Form: Subject object
deactivate Form

Form -> Form: IsValidInfor(newSubject)
activate Form
Form --> Form: validation result
deactivate Form

Form -> Form: IsDuplicateSubject(SubjectId, SubjectName)
activate Form
Form --> Form: duplicate check result
deactivate Form

alt Validation successful
    Form -> BLL: AddNewSubject(newSubject)
    activate BLL
    BLL -> DAL: AddNewSubject(newSubject)
    activate DAL
    DAL -> DB: ExecuteNonQuery("Subject_Insert", parameters)
    activate DB
    DB -> Database: INSERT INTO Subject VALUES(...)
    activate Database
    Database --> DB: Success
    deactivate Database
    DB --> DAL: Success
    deactivate DB
    DAL --> BLL: Success
    deactivate DAL
    BLL --> Form: Success
    deactivate BLL
    
    Form -> Form: XtraMessageBox.Show("Thêm mới môn thi mới thành công!")
    Form -> Form: LoadData()
    activate Form
    Form -> BLL: GetAll()
    activate BLL
    BLL -> DAL: GetAll()
    activate DAL
    DAL -> DB: ExecuteData("Subject_SelectAll")
    activate DB
    DB -> Database: SELECT * FROM Subject
    activate Database
    Database --> DB: Updated DataTable
    deactivate Database
    DB --> DAL: Updated DataTable
    deactivate DB
    DAL --> BLL: Updated DataTable
    deactivate DAL
    BLL --> Form: Updated DataTable
    deactivate BLL
    Form -> Form: grv_DataUser.DataSource = Updated DataTable
    deactivate Form
    
    Form -> Form: ShowHideButton(false)
    Form -> Form: SetEnableControl(false)
    Form --> Admin: Hiển thị thông báo thành công và cập nhật danh sách
    
else Validation failed
    Form -> Form: XtraMessageBox.Show("Lỗi validation")
    Form --> Admin: Hiển thị thông báo lỗi
end

deactivate Form
deactivate Form

@enduml
