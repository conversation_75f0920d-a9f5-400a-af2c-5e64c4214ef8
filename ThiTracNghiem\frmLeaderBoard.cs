﻿using BusinessLogicLayer;
using Entities;
using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using ThiTracNghiem.Common;
using DevExpress.XtraEditors;
using System.Linq;


namespace ThiTracNghiem
{
    public partial class frmLeaderBoard : Form
    {
        public frmLeaderBoard()
        {
            InitializeComponent();
            SetStyle(ControlStyles.ResizeRedraw, true);
        }
        protected override void OnPaintBackground(PaintEventArgs e)
        {
            Rectangle rc = ClientRectangle;
            if (rc.IsEmpty)
                return;
            if (rc.Width == 0 || rc.Height == 0)
                return;
            using (LinearGradientBrush brush = new LinearGradientBrush(rc, Color.White, Color.FromArgb(196, 232, 250), 90F))
            {
                e.Graphics.FillRectangle(brush, rc);
            }
        }

        private void btn_Ok_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void frmLeaderBoard_Load(object sender, EventArgs e)
        {
            try
            {
                //Tạo một đối tượng TestHistory và gán SubjectID từ session hiện tại.
                TestHistory testHistory = new TestHistory();
                testHistory.SubjectID = Session.SubjectID;
                grv_DataUser.AutoGenerateColumns = false; //Đặt AutoGenerateColumns của grv_DataUser thành false để bạn có thể kiểm soát cách hiển thị các cột.
                //Lấy dữ liệu bảng xếp hạng từ lớp BTestHistory.GetLeaderBoard(testHistory) và thiết lập nguồn dữ liệu cho grv_DataUser.
                grv_DataUser.DataSource = BTestHistory.GetLeaderBoard(testHistory);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("Error: " + ex.Message);
            }
        }

        private void grv_DataUser_RowPrePaint(object sender, DataGridViewRowPrePaintEventArgs e)
        {
            //Thiết lập giá trị cho cột "STT" (Số thứ tự) của mỗi dòng, thêm tiền tố "0" nếu chỉ số dòng nhỏ hơn 10 để đảm bảo có 2 chữ số.
            grv_DataUser["STT", e.RowIndex].Value = (e.RowIndex < 10 ? "0" : string.Empty) + (e.RowIndex + 1);
        }
    }
}
