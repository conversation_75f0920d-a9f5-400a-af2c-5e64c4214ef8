@startuml PhanCongGiaoVien_SequenceDiagram

!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

title Sơ đồ tuần tự - <PERSON><PERSON> công Giáo viên cho <PERSON><PERSON> học

actor Admin
participant "frmTeacherSubject" as Form
participant "TeacherSubject" as Entity
participant "BUserAccount" as UserBLL
participant "BSubject" as SubjectBLL
participant "BTeacherSubject" as TeacherSubjectBLL
participant "DUserAccount" as UserDAL
participant "DSubject" as SubjectDAL
participant "DTeacherSubject" as TeacherSubjectDAL
participant "SqlHelper" as DB
database "Database" as Database

== Khởi tạo Form ==
Admin -> Form: Mở form phân công giáo viên
activate Form

Form -> Form: frmTeacherSubject_Load()
Form -> Form: LoadTeachers()
activate Form
Form -> UserBLL: GetByRole("Teacher")
activate UserBLL
UserBLL -> UserDAL: GetByRole("Teacher")
activate UserDAL
UserDAL -> DB: ExecuteData("UserAccount_GetByRole", "Teacher")
activate DB
DB -> Database: SELECT * FROM UserAccount WHERE RoleID = 'Teacher'
activate Database
Database --> DB: Teachers DataTable
deactivate Database
DB --> UserDAL: Teachers DataTable
deactivate DB
UserDAL --> UserBLL: Teachers DataTable
deactivate UserDAL
UserBLL --> Form: Teachers DataTable
deactivate UserBLL
Form -> Form: cbb_Teacher.DataSource = Teachers
deactivate Form

Form -> Form: LoadSubjects()
activate Form
Form -> SubjectBLL: GetAll()
activate SubjectBLL
SubjectBLL -> SubjectDAL: GetAll()
activate SubjectDAL
SubjectDAL -> DB: ExecuteData("Subject_SelectAll")
activate DB
DB -> Database: SELECT * FROM Subject
activate Database
Database --> DB: Subjects DataTable
deactivate Database
DB --> SubjectDAL: Subjects DataTable
deactivate DB
SubjectDAL --> SubjectBLL: Subjects DataTable
deactivate SubjectDAL
SubjectBLL --> Form: Subjects DataTable
deactivate SubjectBLL
Form -> Form: cbb_Subject.DataSource = Subjects
deactivate Form

Form -> Form: LoadTeacherSubjects()
activate Form
Form -> TeacherSubjectBLL: GetByTeacher(teacherId)
activate TeacherSubjectBLL
TeacherSubjectBLL -> TeacherSubjectDAL: GetByTeacher(teacherId)
activate TeacherSubjectDAL
TeacherSubjectDAL -> DB: ExecuteData("TeacherSubject_GetByTeacher", teacherId)
activate DB
DB -> Database: SELECT * FROM TeacherSubject WHERE UserID = teacherId
activate Database
Database --> DB: TeacherSubjects DataTable
deactivate Database
DB --> TeacherSubjectDAL: TeacherSubjects DataTable
deactivate DB
TeacherSubjectDAL --> TeacherSubjectBLL: TeacherSubjects DataTable
deactivate TeacherSubjectDAL
TeacherSubjectBLL --> Form: TeacherSubjects DataTable
deactivate TeacherSubjectBLL
Form -> Form: grv_TeacherSubject.DataSource = TeacherSubjects
deactivate Form

Form --> Admin: Hiển thị form với dữ liệu đã load
deactivate Form

== Thêm phân công mới ==
Admin -> Form: Chọn giáo viên từ ComboBox
Admin -> Form: Chọn môn học từ ComboBox
Admin -> Form: btn_Add_Click()
activate Form

Form -> Form: Kiểm tra cbb_Teacher.SelectedValue != null
Form -> Form: Kiểm tra cbb_Subject.SelectedValue != null

alt Đã chọn giáo viên và môn học
    Form -> Form: Lấy teacherId và subjectId từ ComboBox
    
    Form -> TeacherSubjectBLL: IsTeacherAssigned(teacherId, subjectId)
    activate TeacherSubjectBLL
    TeacherSubjectBLL -> TeacherSubjectDAL: IsTeacherAssigned(teacherId, subjectId)
    activate TeacherSubjectDAL
    TeacherSubjectDAL -> DB: ExecuteScalar("TeacherSubject_CheckExists", parameters)
    activate DB
    DB -> Database: SELECT COUNT(*) FROM TeacherSubject WHERE UserID = ? AND SubjectID = ?
    activate Database
    Database --> DB: Count result
    deactivate Database
    DB --> TeacherSubjectDAL: Boolean result
    deactivate DB
    TeacherSubjectDAL --> TeacherSubjectBLL: Boolean result
    deactivate TeacherSubjectDAL
    TeacherSubjectBLL --> Form: Boolean result
    deactivate TeacherSubjectBLL
    
    alt Chưa được phân công
        create Entity
        Form -> Entity: new TeacherSubject()
        Form -> Entity: UserID = teacherId
        Form -> Entity: SubjectID = subjectId
        Form -> Entity: CreatedBy = Session.LogonUser.Username
        Form -> Entity: ModifiedBy = Session.LogonUser.Username
        
        Form -> TeacherSubjectBLL: AddTeacherSubject(teacherSubject)
        activate TeacherSubjectBLL
        TeacherSubjectBLL -> TeacherSubjectDAL: AddTeacherSubject(teacherSubject)
        activate TeacherSubjectDAL
        TeacherSubjectDAL -> DB: ExecuteNonQuery("TeacherSubject_Insert", parameters)
        activate DB
        DB -> Database: INSERT INTO TeacherSubject VALUES(...)
        activate Database
        Database --> DB: Success
        deactivate Database
        DB --> TeacherSubjectDAL: Success
        deactivate DB
        TeacherSubjectDAL --> TeacherSubjectBLL: Success
        deactivate TeacherSubjectDAL
        TeacherSubjectBLL --> Form: Success
        deactivate TeacherSubjectBLL
        
        Form -> Form: XtraMessageBox.Show("Phân công môn học thành công!")
        Form -> Form: LoadTeacherSubjects()
        activate Form
        Form -> TeacherSubjectBLL: GetByTeacher(teacherId)
        activate TeacherSubjectBLL
        TeacherSubjectBLL -> TeacherSubjectDAL: GetByTeacher(teacherId)
        activate TeacherSubjectDAL
        TeacherSubjectDAL -> DB: ExecuteData("TeacherSubject_GetByTeacher", teacherId)
        activate DB
        DB -> Database: SELECT * FROM TeacherSubject WHERE UserID = teacherId
        activate Database
        Database --> DB: Updated TeacherSubjects DataTable
        deactivate Database
        DB --> TeacherSubjectDAL: Updated TeacherSubjects DataTable
        deactivate DB
        TeacherSubjectDAL --> TeacherSubjectBLL: Updated TeacherSubjects DataTable
        deactivate TeacherSubjectDAL
        TeacherSubjectBLL --> Form: Updated TeacherSubjects DataTable
        deactivate TeacherSubjectBLL
        Form -> Form: grv_TeacherSubject.DataSource = Updated DataTable
        deactivate Form
        
        Form --> Admin: Hiển thị thông báo thành công và cập nhật danh sách
        
    else Đã được phân công
        Form -> Form: XtraMessageBox.Show("Giáo viên đã được phân công môn học này!")
        Form --> Admin: Hiển thị thông báo lỗi
    end
    
else Chưa chọn giáo viên hoặc môn học
    Form -> Form: XtraMessageBox.Show("Vui lòng chọn giáo viên và môn học!")
    Form --> Admin: Hiển thị thông báo lỗi
end

deactivate Form

@enduml
