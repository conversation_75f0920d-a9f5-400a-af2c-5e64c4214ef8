@startuml BaoCao_UseCaseDiagram

!theme plain
skinparam actorStyle awesome
skinparam usecase {
    BackgroundColor LightBlue
    BorderColor DarkBlue
    FontSize 10
}
skinparam rectangle {
    BackgroundColor LightYellow
    BorderColor Orange
    FontStyle bold
}

title Sơ đồ Use Case - Chức năng Báo cáo

' Actors
actor "Admin\n(<PERSON><PERSON><PERSON><PERSON> trị viên)" as admin
actor "Teacher\n(<PERSON><PERSON><PERSON><PERSON> viên)" as teacher

rectangle "<PERSON><PERSON> thống Báo cáo" {

    ' Authentication Package
    package "Xác thực" {
        usecase "Đăng nhập" as login
        usecase "Đăng xuất" as logout
        usecase "<PERSON><PERSON><PERSON> thực quyền truy cập" as checkpermission
    }

    ' Report Management Package - Main Focus
    package "Quản lý Báo cáo" {
        usecase "Xem báo cáo hệ thống" as viewreport
        usecase "Báo cáo theo môn học" as reportsubject
        usecase "<PERSON><PERSON>o cáo theo thời gian" as reporttime
        usecase "<PERSON><PERSON>o cáo kỳ thi" as reportsession
        usecase "Chọn môn học" as selectsubject
        usecase "Chọn khoảng thời gian" as selecttime
        usecase "Chọn kỳ thi" as selectsession
    }

    ' Data Processing Package
    package "Xử lý Dữ liệu" {
        usecase "Lấy dữ liệu báo cáo" as getdata
        usecase "Kiểm tra dữ liệu tồn tại" as checkdata
        usecase "Hiển thị báo cáo" as displayreport
        usecase "Tính toán thống kê" as calculate
    }

    ' Export Package
    package "Xuất Báo cáo" {
        usecase "Xuất báo cáo PDF" as exportpdf
        usecase "Chọn vị trí lưu file" as selectpath
        usecase "Tạo file PDF" as createpdf
        usecase "Lưu file PDF" as savepdf
    }

}

' Actor-UseCase Relationships

' Authentication
admin --> login
admin --> logout
teacher --> login
teacher --> logout

' Main Report Use Cases
admin --> viewreport
teacher --> viewreport

' Admin can access all reports
admin --> reportsubject
admin --> reporttime
admin --> reportsession

' Teacher can only access subject reports
teacher --> reportsubject

' Selection Use Cases
admin --> selectsubject
admin --> selecttime
admin --> selectsession
teacher --> selectsubject

' Data Processing Use Cases
admin --> getdata
admin --> checkdata
admin --> displayreport
admin --> calculate
teacher --> getdata
teacher --> checkdata
teacher --> displayreport
teacher --> calculate

' Export Use Cases
admin --> exportpdf
admin --> selectpath
admin --> createpdf
admin --> savepdf
teacher --> exportpdf
teacher --> selectpath
teacher --> createpdf
teacher --> savepdf

' Include relationships
viewreport ..> checkpermission : <<include>>
reportsubject ..> selectsubject : <<include>>
reportsubject ..> getdata : <<include>>
reportsubject ..> displayreport : <<include>>
reporttime ..> selecttime : <<include>>
reporttime ..> getdata : <<include>>
reporttime ..> displayreport : <<include>>
reportsession ..> selectsession : <<include>>
reportsession ..> getdata : <<include>>
reportsession ..> displayreport : <<include>>
getdata ..> checkdata : <<include>>
displayreport ..> calculate : <<include>>
exportpdf ..> selectpath : <<include>>
exportpdf ..> createpdf : <<include>>
exportpdf ..> savepdf : <<include>>

' Extend relationships
viewreport ..> reportsubject : <<extend>>
viewreport ..> reporttime : <<extend>>
viewreport ..> reportsession : <<extend>>
displayreport ..> exportpdf : <<extend>>

@enduml
