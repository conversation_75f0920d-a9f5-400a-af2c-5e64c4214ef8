@startuml SystemUseCaseDiagram

!theme plain
skinparam actorStyle awesome
skinparam usecase {
    BackgroundColor LightBlue
    BorderColor DarkBlue
    FontSize 10
}
skinparam rectangle {
    BackgroundColor LightYellow
    BorderColor Orange
    FontStyle bold
}

title Sơ đồ Use Case - <PERSON><PERSON> thống Thi Trắc nghiệm

' Actors
actor "Admin\n(<PERSON><PERSON><PERSON><PERSON> trị viên)" as admin
actor "Teacher\n(<PERSON><PERSON><PERSON><PERSON>i<PERSON>)" as teacher
actor "Student\n(<PERSON><PERSON> viên)" as student

rectangle "<PERSON>ệ thống Thi Trắc nghiệm" {

    ' Authentication Package
    package "Xác thực" {
        usecase "Đăng nhập" as login
        usecase "Đăng xuất" as logout
        usecase "<PERSON><PERSON>i mật khẩu" as changepwd
        usecase "Quên mật khẩu" as forgotpwd
    }

    ' Admin Management Package
    package "Quản lý Hệ thống" {
        usecase "Quản lý người dùng" as manageuser
        usecase "Quản lý môn học" as managesubject
        usecase "Phân công giáo viên" as assignteacher
        usecase "<PERSON>ân công học viên" as assignstudent
        usecase "Duyệt đề thi" as approveexam
        usecase "Quản lý kỳ thi" as managesession
        usecase "Sao lưu dữ liệu" as backup
        usecase "Khôi phục dữ liệu" as restore
    }

    ' Teacher Package
    package "Quản lý Giảng dạy" {
        usecase "Quản lý câu hỏi" as managequestion
        usecase "Nhập câu hỏi từ Excel" as importexcel
        usecase "Tạo đề thi" as createexam
    }

    ' Student Package
    package "Tham gia Thi" {
        usecase "Xem kỳ thi" as viewsession
        usecase "Tham gia kỳ thi" as joinsession
        usecase "Làm bài thi" as takeexam
        usecase "Xem kết quả" as viewresult
        usecase "Xem bảng xếp hạng" as viewranking
    }

    ' Reports Package
    package "Báo cáo" {
        usecase "Báo cáo theo môn học" as reportsubject
        usecase "Báo cáo theo thời gian" as reporttime
        usecase "Báo cáo kỳ thi" as reportsession
        usecase "Xuất PDF" as exportpdf
    }

}

' Actor-UseCase Relationships

' Common Authentication (All actors)
admin --> login
admin --> logout
admin --> changepwd
admin --> forgotpwd

teacher --> login
teacher --> logout
teacher --> changepwd
teacher --> forgotpwd

student --> login
student --> logout
student --> changepwd
student --> forgotpwd

' Admin specific use cases
admin --> manageuser
admin --> managesubject
admin --> assignteacher
admin --> assignstudent
admin --> approveexam
admin --> managesession
admin --> backup
admin --> restore

' Admin can view all reports
admin --> reportsubject
admin --> reporttime
admin --> reportsession
admin --> exportpdf

' Teacher specific use cases
teacher --> managequestion
teacher --> importexcel
teacher --> createexam

' Teacher can view subject reports
teacher --> reportsubject
teacher --> exportpdf

' Student specific use cases
student --> viewsession
student --> joinsession
student --> takeexam
student --> viewresult
student --> viewranking

' Extend relationships
joinsession ..> takeexam : <<extend>>
takeexam ..> viewresult : <<extend>>
viewresult ..> viewranking : <<extend>>

@enduml
