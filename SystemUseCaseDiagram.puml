@startuml SystemUseCaseDiagram

!theme plain
skinparam actorStyle awesome
skinparam usecase {
    BackgroundColor LightBlue
    BorderColor DarkBlue
    FontSize 10
}
skinparam rectangle {
    BackgroundColor LightYellow
    BorderColor Orange
    FontStyle bold
}

title Sơ đồ Use Case - Quản lý Môn học

' Actors
actor "Admin\n(<PERSON><PERSON>ản trị viên)" as admin

rectangle "Hệ thống Quản lý Môn học" {

    ' Authentication Package
    package "Xác thực" {
        usecase "Đăng nhập" as login
        usecase "Đăng xuất" as logout
    }

    ' Subject Management Package - Main Focus
    package "Quản lý Môn học" {
        usecase "Quản lý môn học" as managesubject
        usecase "Thêm môn học" as addsubject
        usecase "Sửa thông tin môn học" as editsubject
        usecase "Xóa môn học" as deletesubject
        usecase "Tì<PERSON> kiếm môn học" as searchsubject
        usecase "X<PERSON> danh sách môn học" as viewsubjects
        usecase "Kiểm tra tồn tại môn học" as checksubject
    }

    ' Validation Package
    package "<PERSON><PERSON><PERSON> thực dữ liệu" {
        usecase "Xác thực thông tin môn học" as validatesubject
        usecase "Kiểm tra trùng lặp" as checkduplicate
    }

}

' Actor-UseCase Relationships

' Authentication
admin --> login
admin --> logout

' Main Subject Management Use Cases
admin --> managesubject
admin --> addsubject
admin --> editsubject
admin --> deletesubject
admin --> searchsubject
admin --> viewsubjects
admin --> checksubject

' Validation Use Cases
admin --> validatesubject
admin --> checkduplicate

' Include relationships
managesubject ..> viewsubjects : <<include>>
addsubject ..> validatesubject : <<include>>
addsubject ..> checkduplicate : <<include>>
editsubject ..> validatesubject : <<include>>
editsubject ..> checksubject : <<include>>
deletesubject ..> checksubject : <<include>>

' Extend relationships
managesubject ..> addsubject : <<extend>>
managesubject ..> editsubject : <<extend>>
managesubject ..> deletesubject : <<extend>>
managesubject ..> searchsubject : <<extend>>

@enduml
