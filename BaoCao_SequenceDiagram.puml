@startuml BaoCao_SequenceDiagram

!theme plain
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

title Sơ đồ tuần tự - <PERSON><PERSON><PERSON> cá<PERSON> theo <PERSON> học

actor "Admin/Teacher" as User
participant "NewFrmReportBySubject" as Form
participant "BSubject" as SubjectBLL
participant "BTestHistory" as TestHistoryBLL
participant "ReportHelper" as Helper
participant "DSubject" as SubjectDAL
participant "DTestHistory" as TestHistoryDAL
participant "SqlHelper" as DB
database "Database" as Database

== Khởi tạo Form Báo cáo ==
User -> Form: Mở form báo cáo theo môn học
activate Form

Form -> Form: NewFrmReportBySubject_Load()
Form -> Form: LoadData()
activate Form
Form -> SubjectBLL: GetAll()
activate SubjectBLL
SubjectBLL -> SubjectDAL: GetAll()
activate SubjectDAL
SubjectDAL -> DB: ExecuteData("Subject_SelectAll")
activate DB
DB -> Database: SELECT * FROM Subject
activate Database
Database --> DB: Subjects DataTable
deactivate Database
DB --> SubjectDAL: Subjects DataTable
deactivate DB
SubjectDAL --> SubjectBLL: Subjects DataTable
deactivate SubjectDAL
SubjectBLL --> Form: Subjects DataTable
deactivate SubjectBLL
Form -> Form: cbb_MonThi.DataSource = Subjects
Form -> Form: cbb_MonThi.DisplayMember = "SubjectName"
Form -> Form: cbb_MonThi.ValueMember = "SubjectID"
deactivate Form

Form --> User: Hiển thị form với danh sách môn học
deactivate Form

== Xem báo cáo theo môn học ==
User -> Form: Chọn môn học từ cbb_MonThi
User -> Form: btn_XemBaoCao_Click()
activate Form

Form -> Form: GetReportData()
activate Form
Form -> Form: subjectId = cbb_MonThi.SelectedValue.ToString()
Form -> TestHistoryBLL: GetReportBySubject(subjectId)
activate TestHistoryBLL
TestHistoryBLL -> TestHistoryDAL: GetReportBySubject(subjectId)
activate TestHistoryDAL
TestHistoryDAL -> DB: ExecuteData("TestHistory_GetReportBySubject", subjectId)
activate DB
DB -> Database: SELECT * FROM TestHistory WHERE SubjectID = subjectId
activate Database
Database --> DB: Report DataTable
deactivate Database
DB --> TestHistoryDAL: Report DataTable
deactivate DB
TestHistoryDAL --> TestHistoryBLL: Report DataTable
deactivate TestHistoryDAL
TestHistoryBLL --> Form: Report DataTable
deactivate TestHistoryBLL
Form --> Form: Return Report DataTable
deactivate Form

alt Có dữ liệu báo cáo
    Form -> Form: subjectName = cbb_MonThi.Text
    Form -> Helper: ShowSubjectReport(subjectName, dtData)
    activate Helper
    Helper -> Helper: Tạo báo cáo với ReportViewer
    Helper -> Helper: Thiết lập DataSource và parameters
    Helper -> Helper: Hiển thị báo cáo trên DocumentViewer
    Helper --> Form: Báo cáo đã được hiển thị
    deactivate Helper
    Form --> User: Hiển thị báo cáo trên giao diện
    
else Không có dữ liệu
    Form -> Form: XtraMessageBox.Show("Không có dữ liệu để hiển thị báo cáo.")
    Form --> User: Hiển thị thông báo không có dữ liệu
end

deactivate Form

== Báo cáo theo thời gian ==
note over User, Database: Tương tự như báo cáo theo môn học

User -> Form: Mở NewFrmReportByTime
activate Form
Form -> Form: dpk_StartDate.Value = DateTime.Now.AddDays(-30)
Form -> Form: dpk_EndDate.Value = DateTime.Now
Form --> User: Hiển thị form với ngày mặc định
deactivate Form

User -> Form: Chọn khoảng thời gian
User -> Form: btn_XemBaoCao_Click()
activate Form
Form -> Form: startDate = dpk_StartDate.Value
Form -> Form: endDate = dpk_EndDate.Value
Form -> TestHistoryBLL: GetReportByTime(startDate, endDate)
activate TestHistoryBLL
TestHistoryBLL -> TestHistoryDAL: GetReportByTime(startDate, endDate)
activate TestHistoryDAL
TestHistoryDAL -> DB: ExecuteData("TestHistory_GetReportByTime", startDate, endDate)
activate DB
DB -> Database: SELECT * FROM TestHistory WHERE TestDate BETWEEN startDate AND endDate
activate Database
Database --> DB: Report DataTable
deactivate Database
DB --> TestHistoryDAL: Report DataTable
deactivate DB
TestHistoryDAL --> TestHistoryBLL: Report DataTable
deactivate TestHistoryDAL
TestHistoryBLL --> Form: Report DataTable
deactivate TestHistoryBLL

alt Có dữ liệu báo cáo
    Form -> Form: reportTime = "Từ ngày ... đến ngày ..."
    Form -> Helper: ShowTimeReport(reportTime, dtData)
    activate Helper
    Helper -> Helper: Tạo và hiển thị báo cáo thời gian
    Helper --> Form: Báo cáo đã được hiển thị
    deactivate Helper
    Form --> User: Hiển thị báo cáo theo thời gian
    
else Không có dữ liệu
    Form -> Form: XtraMessageBox.Show("Không có dữ liệu để hiển thị báo cáo.")
    Form --> User: Hiển thị thông báo không có dữ liệu
end

deactivate Form

== Báo cáo kỳ thi ==
note over User, Database: Báo cáo kỳ thi với ExamSession và UserExamSession

User -> Form: Mở NewFrmReportExamSession
activate Form
Form -> Form: LoadExamSessions()
Form --> User: Hiển thị danh sách kỳ thi
deactivate Form

User -> Form: Chọn kỳ thi và btn_ViewReport_Click()
activate Form
Form -> Form: sessionId = cbb_ExamSession.SelectedValue
Form -> Form: examSession = BExamSession.GetById(sessionId)
Form -> Form: dtUsers = BUserExamSession.GetBySession(sessionId)
Form -> Form: Hiển thị thông tin kỳ thi và danh sách người tham gia
Form --> User: Hiển thị báo cáo kỳ thi
deactivate Form

User -> Form: btn_ExportPdf_Click()
activate Form
Form -> Helper: ExportToPdf(reportPath, dtUsers, dataSetName, parameters, fileName)
activate Helper
Helper -> Helper: Tạo file PDF từ dữ liệu báo cáo
Helper -> Helper: Lưu file PDF vào vị trí đã chọn
Helper --> Form: File PDF đã được tạo
deactivate Helper
Form --> User: Thông báo xuất PDF thành công
deactivate Form

@enduml
