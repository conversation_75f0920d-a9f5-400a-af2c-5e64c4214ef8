﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="QL_THITRACNGHIEM2DataSet" targetNamespace="http://tempuri.org/QL_THITRACNGHIEM2DataSet.xsd" xmlns:mstns="http://tempuri.org/QL_THITRACNGHIEM2DataSet.xsd" xmlns="http://tempuri.org/QL_THITRACNGHIEM2DataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="QL_THITRACNGHIEM2ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="QL_THITRACNGHIEM2ConnectionString (Settings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.ThiTracNghiem.Properties.Settings.GlobalReference.Default.QL_THITRACNGHIEM2ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TestHistory_ReportBySubjectTableAdapter" GeneratorDataComponentClassName="TestHistory_ReportBySubjectTableAdapter" Name="TestHistory_ReportBySubject" UserDataComponentName="TestHistory_ReportBySubjectTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="QL_THITRACNGHIEM2ConnectionString (Settings)" DbObjectName="QL_THITRACNGHIEM2.dbo.TestHistory_ReportBySubject" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.TestHistory_ReportBySubject</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@SubjectID" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TestId" DataSetColumn="TestId" />
              <Mapping SourceColumn="UserID" DataSetColumn="UserID" />
              <Mapping SourceColumn="Fullname" DataSetColumn="Fullname" />
              <Mapping SourceColumn="SubjectID" DataSetColumn="SubjectID" />
              <Mapping SourceColumn="TestDate" DataSetColumn="TestDate" />
              <Mapping SourceColumn="CorrectAnswer" DataSetColumn="CorrectAnswer" />
              <Mapping SourceColumn="TotalQuestion" DataSetColumn="TotalQuestion" />
              <Mapping SourceColumn="Mark" DataSetColumn="Mark" />
              <Mapping SourceColumn="CreatedBy" DataSetColumn="CreatedBy" />
              <Mapping SourceColumn="CreatedAt" DataSetColumn="CreatedAt" />
              <Mapping SourceColumn="ModifiedBy" DataSetColumn="ModifiedBy" />
              <Mapping SourceColumn="ModifiedAt" DataSetColumn="ModifiedAt" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TestHistory_ReportByTimeTableAdapter" GeneratorDataComponentClassName="TestHistory_ReportByTimeTableAdapter" Name="TestHistory_ReportByTime" UserDataComponentName="TestHistory_ReportByTimeTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="QL_THITRACNGHIEM2ConnectionString (Settings)" DbObjectName="QL_THITRACNGHIEM2.dbo.TestHistory_ReportByTime" DbObjectType="StoredProcedure" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>dbo.TestHistory_ReportByTime</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@StartDate" Precision="23" ProviderType="DateTime" Scale="3" Size="8" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@EndDate" Precision="23" ProviderType="DateTime" Scale="3" Size="8" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TestId" DataSetColumn="TestId" />
              <Mapping SourceColumn="Fullname" DataSetColumn="Fullname" />
              <Mapping SourceColumn="SubjectName" DataSetColumn="SubjectName" />
              <Mapping SourceColumn="TestDate" DataSetColumn="TestDate" />
              <Mapping SourceColumn="CorrectAnswer" DataSetColumn="CorrectAnswer" />
              <Mapping SourceColumn="TotalQuestion" DataSetColumn="TotalQuestion" />
              <Mapping SourceColumn="Mark" DataSetColumn="Mark" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="QL_THITRACNGHIEM2DataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="True" msprop:Generator_UserDSName="QL_THITRACNGHIEM2DataSet" msprop:Generator_DataSetName="QL_THITRACNGHIEM2DataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="TestHistory_ReportBySubject" msprop:Generator_RowEvHandlerName="TestHistory_ReportBySubjectRowChangeEventHandler" msprop:Generator_RowDeletedName="TestHistory_ReportBySubjectRowDeleted" msprop:Generator_RowDeletingName="TestHistory_ReportBySubjectRowDeleting" msprop:Generator_RowEvArgName="TestHistory_ReportBySubjectRowChangeEvent" msprop:Generator_TablePropName="TestHistory_ReportBySubject" msprop:Generator_RowChangedName="TestHistory_ReportBySubjectRowChanged" msprop:Generator_RowChangingName="TestHistory_ReportBySubjectRowChanging" msprop:Generator_TableClassName="TestHistory_ReportBySubjectDataTable" msprop:Generator_RowClassName="TestHistory_ReportBySubjectRow" msprop:Generator_TableVarName="tableTestHistory_ReportBySubject" msprop:Generator_UserTableName="TestHistory_ReportBySubject">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TestId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnPropNameInRow="TestId" msprop:Generator_ColumnPropNameInTable="TestIdColumn" msprop:Generator_ColumnVarNameInTable="columnTestId" msprop:Generator_UserColumnName="TestId" type="xs:int" />
              <xs:element name="UserID" msprop:Generator_ColumnPropNameInRow="UserID" msprop:Generator_ColumnPropNameInTable="UserIDColumn" msprop:Generator_ColumnVarNameInTable="columnUserID" msprop:Generator_UserColumnName="UserID" type="xs:int" />
              <xs:element name="Fullname" msdata:ReadOnly="true" msprop:Generator_ColumnPropNameInRow="Fullname" msprop:Generator_ColumnPropNameInTable="FullnameColumn" msprop:Generator_ColumnVarNameInTable="columnFullname" msprop:Generator_UserColumnName="Fullname" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SubjectID" msprop:Generator_ColumnPropNameInRow="SubjectID" msprop:Generator_ColumnPropNameInTable="SubjectIDColumn" msprop:Generator_ColumnVarNameInTable="columnSubjectID" msprop:Generator_UserColumnName="SubjectID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="TestDate" msprop:Generator_ColumnPropNameInRow="TestDate" msprop:Generator_ColumnPropNameInTable="TestDateColumn" msprop:Generator_ColumnVarNameInTable="columnTestDate" msprop:Generator_UserColumnName="TestDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="CorrectAnswer" msprop:Generator_ColumnPropNameInRow="CorrectAnswer" msprop:Generator_ColumnPropNameInTable="CorrectAnswerColumn" msprop:Generator_ColumnVarNameInTable="columnCorrectAnswer" msprop:Generator_UserColumnName="CorrectAnswer" type="xs:int" minOccurs="0" />
              <xs:element name="TotalQuestion" msprop:Generator_ColumnPropNameInRow="TotalQuestion" msprop:Generator_ColumnPropNameInTable="TotalQuestionColumn" msprop:Generator_ColumnVarNameInTable="columnTotalQuestion" msprop:Generator_UserColumnName="TotalQuestion" type="xs:int" minOccurs="0" />
              <xs:element name="Mark" msprop:Generator_ColumnPropNameInRow="Mark" msprop:Generator_ColumnPropNameInTable="MarkColumn" msprop:Generator_ColumnVarNameInTable="columnMark" msprop:Generator_UserColumnName="Mark" type="xs:int" minOccurs="0" />
              <xs:element name="CreatedBy" msprop:Generator_ColumnPropNameInRow="CreatedBy" msprop:Generator_ColumnPropNameInTable="CreatedByColumn" msprop:Generator_ColumnVarNameInTable="columnCreatedBy" msprop:Generator_UserColumnName="CreatedBy" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedAt" msprop:Generator_ColumnPropNameInRow="CreatedAt" msprop:Generator_ColumnPropNameInTable="CreatedAtColumn" msprop:Generator_ColumnVarNameInTable="columnCreatedAt" msprop:Generator_UserColumnName="CreatedAt" type="xs:dateTime" minOccurs="0" />
              <xs:element name="ModifiedBy" msprop:Generator_ColumnPropNameInRow="ModifiedBy" msprop:Generator_ColumnPropNameInTable="ModifiedByColumn" msprop:Generator_ColumnVarNameInTable="columnModifiedBy" msprop:Generator_UserColumnName="ModifiedBy" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ModifiedAt" msprop:Generator_ColumnPropNameInRow="ModifiedAt" msprop:Generator_ColumnPropNameInTable="ModifiedAtColumn" msprop:Generator_ColumnVarNameInTable="columnModifiedAt" msprop:Generator_UserColumnName="ModifiedAt" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TestHistory_ReportByTime" msprop:Generator_RowEvHandlerName="TestHistory_ReportByTimeRowChangeEventHandler" msprop:Generator_RowDeletedName="TestHistory_ReportByTimeRowDeleted" msprop:Generator_RowDeletingName="TestHistory_ReportByTimeRowDeleting" msprop:Generator_RowEvArgName="TestHistory_ReportByTimeRowChangeEvent" msprop:Generator_TablePropName="TestHistory_ReportByTime" msprop:Generator_RowChangedName="TestHistory_ReportByTimeRowChanged" msprop:Generator_RowChangingName="TestHistory_ReportByTimeRowChanging" msprop:Generator_TableClassName="TestHistory_ReportByTimeDataTable" msprop:Generator_RowClassName="TestHistory_ReportByTimeRow" msprop:Generator_TableVarName="tableTestHistory_ReportByTime" msprop:Generator_UserTableName="TestHistory_ReportByTime">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TestId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnPropNameInRow="TestId" msprop:Generator_ColumnPropNameInTable="TestIdColumn" msprop:Generator_ColumnVarNameInTable="columnTestId" msprop:Generator_UserColumnName="TestId" type="xs:int" />
              <xs:element name="Fullname" msdata:ReadOnly="true" msprop:Generator_ColumnPropNameInRow="Fullname" msprop:Generator_ColumnPropNameInTable="FullnameColumn" msprop:Generator_ColumnVarNameInTable="columnFullname" msprop:Generator_UserColumnName="Fullname" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SubjectName" msdata:ReadOnly="true" msprop:Generator_ColumnPropNameInRow="SubjectName" msprop:Generator_ColumnPropNameInTable="SubjectNameColumn" msprop:Generator_ColumnVarNameInTable="columnSubjectName" msprop:Generator_UserColumnName="SubjectName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="TestDate" msdata:ReadOnly="true" msprop:Generator_ColumnPropNameInRow="TestDate" msprop:Generator_ColumnPropNameInTable="TestDateColumn" msprop:Generator_ColumnVarNameInTable="columnTestDate" msprop:Generator_UserColumnName="TestDate" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CorrectAnswer" msprop:Generator_ColumnPropNameInRow="CorrectAnswer" msprop:Generator_ColumnPropNameInTable="CorrectAnswerColumn" msprop:Generator_ColumnVarNameInTable="columnCorrectAnswer" msprop:Generator_UserColumnName="CorrectAnswer" type="xs:int" minOccurs="0" />
              <xs:element name="TotalQuestion" msprop:Generator_ColumnPropNameInRow="TotalQuestion" msprop:Generator_ColumnPropNameInTable="TotalQuestionColumn" msprop:Generator_ColumnVarNameInTable="columnTotalQuestion" msprop:Generator_UserColumnName="TotalQuestion" type="xs:int" minOccurs="0" />
              <xs:element name="Mark" msprop:Generator_ColumnPropNameInRow="Mark" msprop:Generator_ColumnPropNameInTable="MarkColumn" msprop:Generator_ColumnVarNameInTable="columnMark" msprop:Generator_UserColumnName="Mark" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TestHistory_ReportBySubject" />
      <xs:field xpath="mstns:TestId" />
    </xs:unique>
    <xs:unique name="TestHistory_ReportByTime_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TestHistory_ReportByTime" />
      <xs:field xpath="mstns:TestId" />
    </xs:unique>
  </xs:element>
</xs:schema>